#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的查询速度测试
"""

import time
import concurrent.futures

def simulate_request(delay=0.2):
    """模拟API请求"""
    time.sleep(delay)
    return "success"

def test_serial_vs_concurrent():
    """测试串行vs并发的性能差异"""
    num_requests = 15  # 模拟5个商品 * 3天数据
    delay = 0.2  # 每个请求0.2秒
    
    print("查询速度优化测试")
    print("=" * 40)
    print(f"测试场景: {num_requests}个请求，每个请求{delay}秒延迟")
    print()
    
    # 串行测试（慢速模式）
    print("【慢速模式】串行处理:")
    start_time = time.time()
    for i in range(num_requests):
        simulate_request(delay)
        print(f"  完成 {i+1}/{num_requests}")
    serial_time = time.time() - start_time
    print(f"  总耗时: {serial_time:.1f}秒")
    print()
    
    # 并发测试（快速模式）
    print("【快速模式】并发处理 (3个并发):")
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(simulate_request, delay) for _ in range(num_requests)]
        completed = 0
        for future in concurrent.futures.as_completed(futures):
            future.result()
            completed += 1
            print(f"  完成 {completed}/{num_requests}")
    concurrent_time = time.time() - start_time
    print(f"  总耗时: {concurrent_time:.1f}秒")
    print()
    
    # 高并发测试（超快模式）
    print("【超快模式】高并发处理 (5个并发):")
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(simulate_request, delay) for _ in range(num_requests)]
        completed = 0
        for future in concurrent.futures.as_completed(futures):
            future.result()
            completed += 1
            print(f"  完成 {completed}/{num_requests}")
    high_concurrent_time = time.time() - start_time
    print(f"  总耗时: {high_concurrent_time:.1f}秒")
    print()
    
    # 性能对比
    print("性能提升对比:")
    print("=" * 40)
    print(f"慢速模式: {serial_time:.1f}秒 (基准)")
    print(f"快速模式: {concurrent_time:.1f}秒 (提升 {serial_time/concurrent_time:.1f}倍)")
    print(f"超快模式: {high_concurrent_time:.1f}秒 (提升 {serial_time/high_concurrent_time:.1f}倍)")
    print()
    
    # 实际应用场景
    print("实际应用场景预估:")
    print("=" * 40)
    scenarios = [
        ("3个商品, 30天", 3 * 31),  # 3个商品标题 + 3*30天数据
        ("5个商品, 30天", 5 * 31),  # 5个商品标题 + 5*30天数据
        ("10个商品, 30天", 10 * 31), # 10个商品标题 + 10*30天数据
    ]
    
    for scenario_name, total_requests in scenarios:
        serial_est = total_requests * delay
        concurrent_est = serial_est / 3  # 3个并发
        high_concurrent_est = serial_est / 5  # 5个并发
        
        print(f"{scenario_name}:")
        print(f"  慢速模式: {serial_est/60:.1f}分钟")
        print(f"  快速模式: {concurrent_est/60:.1f}分钟")
        print(f"  超快模式: {high_concurrent_est/60:.1f}分钟")
        print()

if __name__ == "__main__":
    test_serial_vs_concurrent()
