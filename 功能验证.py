#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具功能验证脚本
验证所有核心功能是否正常工作
"""

import json
import time
from pathlib import Path
from data_collector import DataCollector

def check_files():
    """检查必要文件"""
    print("1. 检查必要文件...")
    
    required_files = {
        "main.py": "主程序文件",
        "data_collector.py": "数据采集模块",
        "cookie_exporter.py": "Cookie管理模块", 
        "category_parser.py": "类目解析模块",
        "类目响应数据.md": "类目数据源文件",
        "请求载荷源码格式.md": "请求格式文档",
        "响应数据格式.md": "响应格式文档"
    }
    
    missing_files = []
    for file_path, description in required_files.items():
        if Path(file_path).exists():
            print(f"   ✓ {file_path} - {description}")
        else:
            print(f"   ✗ {file_path} - {description} (缺失)")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_data_files():
    """检查数据文件"""
    print("\n2. 检查数据文件...")
    
    data_files = {
        "data/Category data.txt": "类目数据文件",
        "data/cookies.txt": "Cookie文件1",
        "data/cookies2.txt": "Cookie文件2"
    }
    
    missing_files = []
    for file_path, description in data_files.items():
        if Path(file_path).exists():
            print(f"   ✓ {file_path} - {description}")
        else:
            print(f"   ✗ {file_path} - {description} (缺失)")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_category_data():
    """测试类目数据"""
    print("\n3. 测试类目数据...")
    
    try:
        category_file = Path("data/Category data.txt")
        if not category_file.exists():
            print("   ✗ 类目数据文件不存在")
            return False
            
        with open(category_file, 'r', encoding='utf-8') as f:
            category_data = json.load(f)
            
        if not category_data:
            print("   ✗ 类目数据为空")
            return False
            
        print(f"   ✓ 类目数据加载成功，包含 {len(category_data)} 个一级类目")
        
        # 显示前3个类目
        for i, category in enumerate(list(category_data.keys())[:3], 1):
            print(f"     {i}. {category}")
            
        return True
        
    except Exception as e:
        print(f"   ✗ 类目数据测试失败: {e}")
        return False

def test_collector_basic():
    """测试采集器基础功能"""
    print("\n4. 测试采集器基础功能...")
    
    try:
        # 创建采集器
        collector = DataCollector()
        print("   ✓ 采集器创建成功")
        
        # 加载类目数据
        if collector.load_category_data():
            print("   ✓ 类目数据加载成功")
        else:
            print("   ✗ 类目数据加载失败")
            return False
            
        # 加载Cookie
        cookies = collector.load_cookies()
        if cookies:
            print(f"   ✓ Cookie加载成功，共 {len(cookies)} 个")
        else:
            print("   ✗ Cookie加载失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ✗ 采集器基础功能测试失败: {e}")
        return False

def test_request_generation():
    """测试请求生成"""
    print("\n5. 测试请求载荷生成...")
    
    try:
        collector = DataCollector()
        collector.load_category_data()
        
        # 设置测试筛选条件
        first_category = list(collector.category_data.keys())[0]
        test_filters = {
            "日期": "2025-07-21-2025-07-27",
            "一级类目": first_category,
            "二级类目": "",
            "三级类目": "",
            "四级类目": "",
            "售卖渠道": "全部",
            "售卖形式": "全部",
            "品牌商品": "全部",
            "大牌大补": "全部"
        }
        
        collector.set_filters(test_filters)
        
        # 生成请求载荷
        payload = collector.generate_request_payload()
        if payload:
            print("   ✓ 请求载荷生成成功")
            print(f"     模块: {payload.get('module')}")
            print(f"     时间范围: {payload.get('timeRange')}")
            print(f"     参数数量: {len(payload.get('param', []))}")
            return True
        else:
            print("   ✗ 请求载荷生成失败")
            return False
            
    except Exception as e:
        print(f"   ✗ 请求载荷生成测试失败: {e}")
        return False

def test_network_request():
    """测试网络请求"""
    print("\n6. 测试网络请求...")
    
    try:
        collector = DataCollector()
        collector.load_category_data()
        
        # 设置测试筛选条件
        first_category = list(collector.category_data.keys())[0]
        test_filters = {
            "日期": "2025-07-21-2025-07-27",
            "一级类目": first_category,
            "二级类目": "",
            "三级类目": "",
            "四级类目": "",
            "售卖渠道": "全部",
            "售卖形式": "全部",
            "品牌商品": "全部",
            "大牌大补": "全部"
        }
        
        collector.set_filters(test_filters)
        payload = collector.generate_request_payload()
        
        # 执行网络请求
        response_data = collector.execute_request(payload)
        
        if response_data:
            print("   ✓ 网络请求成功")
            
            # 检查响应数据
            if 'data' in response_data and 'data' in response_data['data']:
                data_list = response_data['data']['data']
                print(f"   ✓ 获取到 {len(data_list)} 条数据")
                
                if data_list:
                    # 测试数据处理
                    processed_data = collector.process_response_data(response_data)
                    if processed_data:
                        print(f"   ✓ 数据处理成功，处理了 {len(processed_data)} 条数据")
                        return True
                    else:
                        print("   ✗ 数据处理失败")
                        return False
                else:
                    print("   ⚠ 获取到的数据为空（可能是筛选条件导致）")
                    return True
            else:
                print("   ✗ 响应数据格式错误")
                return False
        else:
            print("   ✗ 网络请求失败")
            return False
            
    except Exception as e:
        print(f"   ✗ 网络请求测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("快手采集工具功能验证")
    print("=" * 60)
    
    tests = [
        ("文件检查", check_files),
        ("数据文件检查", check_data_files),
        ("类目数据测试", test_category_data),
        ("采集器基础功能", test_collector_basic),
        ("请求生成测试", test_request_generation),
        ("网络请求测试", test_network_request)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"验证结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能验证通过！采集工具可以正常使用。")
        print("\n使用建议:")
        print("1. 运行 python main.py 启动主程序")
        print("2. 确保已解析类目数据和登录获取Cookie")
        print("3. 设置筛选条件后点击'开始采集'")
        print("4. 采集完成后可以导出数据")
    else:
        print("⚠️  部分功能验证失败，请检查相关问题。")
        print("\n常见解决方案:")
        print("1. 确保所有必要文件存在")
        print("2. 运行解析类目功能")
        print("3. 重新登录获取有效Cookie")
        print("4. 检查网络连接")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
