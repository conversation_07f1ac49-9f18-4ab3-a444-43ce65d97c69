#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示数据过滤设置界面
"""

import sys
import json
from pathlib import Path

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                QWidget, QPushButton, QLabel, QLineEdit, QGroupBox)
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
except ImportError:
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                    QWidget, QPushButton, QLabel, QLineEdit, QGroupBox)
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
    except ImportError:
        print("错误：需要安装 PyQt5 或 PyQt6")
        sys.exit(1)


class FilterSettingsDemo(QMainWindow):
    """数据过滤设置演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.filter_settings = {
            "成交指数最小值": 1500,
            "成交指数最大值": 999999,
            "渠道占比最小值": 85,
            "渠道占比最大值": 105
        }
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据过滤设置演示")
        self.setGeometry(200, 200, 800, 300)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 添加说明标签
        title_label = QLabel("数据过滤设置功能演示")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 创建数据过滤设置区域
        filter_widget = self.create_filter_widget()
        main_layout.addWidget(filter_widget)
        
        # 添加演示按钮
        demo_layout = QHBoxLayout()
        
        test_btn = QPushButton("测试过滤逻辑")
        test_btn.clicked.connect(self.test_filter_logic)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        demo_layout.addStretch()
        demo_layout.addWidget(test_btn)
        demo_layout.addWidget(save_btn)
        demo_layout.addStretch()
        
        main_layout.addLayout(demo_layout)
        main_layout.addStretch()
    
    def create_filter_widget(self):
        """创建数据过滤设置区域"""
        # 创建数据过滤设置组
        data_filter_group = QGroupBox("数据过滤设置")
        data_filter_group.setFixedHeight(172)  # 增加高度以适应20px间距
        data_filter_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 13px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 13px;
                padding: 0 6px 0 6px;
            }
        """)
        
        # 主水平布局：左侧50%过滤设置，右侧50%功能按钮
        main_layout = QHBoxLayout(data_filter_group)
        main_layout.setContentsMargins(15, 15, 15, 15)  # 进一步缩小边距
        main_layout.setSpacing(20)  # 进一步缩小间距

        # 左侧：过滤设置区域
        filter_settings_widget = QWidget()
        filter_settings_layout = QVBoxLayout(filter_settings_widget)
        filter_settings_layout.setContentsMargins(0, 0, 0, 0)
        filter_settings_layout.setSpacing(20)  # 设置输入框间距为20px
        
        # 成交指数区间设置
        trade_index_layout = QHBoxLayout()
        trade_index_label = QLabel("成交指数区间")
        trade_index_label.setFixedWidth(110)  # 缩小标签宽度
        trade_index_label.setStyleSheet("font-size: 14px; font-weight: bold;")

        self.trade_index_min_input = QLineEdit()
        self.trade_index_min_input.setPlaceholderText("最小值")
        self.trade_index_min_input.setFixedSize(85, 40)  # 设置为85px*40px
        self.trade_index_min_input.setText(str(self.filter_settings["成交指数最小值"]))

        trade_index_separator = QLabel("~")
        trade_index_separator.setAlignment(Qt.AlignCenter)
        trade_index_separator.setFixedWidth(20)  # 缩小分隔符宽度
        trade_index_separator.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            border: none;
            background: transparent;
            color: #666666;
        """)

        self.trade_index_max_input = QLineEdit()
        self.trade_index_max_input.setPlaceholderText("最大值")
        self.trade_index_max_input.setFixedSize(85, 40)  # 设置为85px*40px
        self.trade_index_max_input.setText(str(self.filter_settings["成交指数最大值"]))
        
        trade_index_layout.addWidget(trade_index_label)
        trade_index_layout.addWidget(self.trade_index_min_input)
        trade_index_layout.addWidget(trade_index_separator)
        trade_index_layout.addWidget(self.trade_index_max_input)
        trade_index_layout.addStretch()

        # 设置输入框间距为3px
        trade_index_layout.setSpacing(3)
        
        # 渠道占比区间设置
        channel_ratio_layout = QHBoxLayout()
        channel_ratio_label = QLabel("渠道占比区间")
        channel_ratio_label.setFixedWidth(110)  # 缩小标签宽度
        channel_ratio_label.setStyleSheet("font-size: 14px; font-weight: bold;")

        self.channel_ratio_min_input = QLineEdit()
        self.channel_ratio_min_input.setPlaceholderText("最小值%")
        self.channel_ratio_min_input.setFixedSize(85, 40)  # 设置为85px*40px
        self.channel_ratio_min_input.setText(str(self.filter_settings["渠道占比最小值"]))

        channel_ratio_separator = QLabel("~")
        channel_ratio_separator.setAlignment(Qt.AlignCenter)
        channel_ratio_separator.setFixedWidth(20)  # 缩小分隔符宽度
        channel_ratio_separator.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            border: none;
            background: transparent;
            color: #666666;
        """)

        self.channel_ratio_max_input = QLineEdit()
        self.channel_ratio_max_input.setPlaceholderText("最大值%")
        self.channel_ratio_max_input.setFixedSize(85, 40)  # 设置为85px*40px
        self.channel_ratio_max_input.setText(str(self.filter_settings["渠道占比最大值"]))
        
        channel_ratio_layout.addWidget(channel_ratio_label)
        channel_ratio_layout.addWidget(self.channel_ratio_min_input)
        channel_ratio_layout.addWidget(channel_ratio_separator)
        channel_ratio_layout.addWidget(self.channel_ratio_max_input)
        channel_ratio_layout.addStretch()

        # 设置输入框间距为3px
        channel_ratio_layout.setSpacing(3)
        
        filter_settings_layout.addLayout(trade_index_layout)
        filter_settings_layout.addLayout(channel_ratio_layout)
        
        # 右侧：功能按钮区域（模拟）
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        buttons = ["登录", "开始采集", "停止采集", "导出数据", "解析类目"]
        colors = ["#4CAF50", "#2196F3", "#f44336", "#FF9800", "#9C27B0"]
        
        for i, (text, color) in enumerate(zip(buttons, colors)):
            btn = QPushButton(text)
            btn.setMinimumHeight(25)
            btn.setMinimumWidth(60)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                    font-size: 11px;
                    padding: 4px 8px;
                }}
                QPushButton:hover {{
                    opacity: 0.8;
                }}
            """)
            button_layout.addWidget(btn)
        
        # 添加到主布局 - 调整比例以适应放大后的元素
        main_layout.addWidget(filter_settings_widget, 3)  # 60%
        main_layout.addWidget(button_widget, 2)  # 40%
        
        # 设置输入框样式
        input_style = """
            QLineEdit {
                border: 1px solid #cccccc;
                border-radius: 5px;
                padding: 6px;
                background-color: white;
                font-size: 15px;
                box-sizing: border-box;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
                background-color: #f8f9fa;
            }
        """
        
        for input_widget in [self.trade_index_min_input, self.trade_index_max_input,
                           self.channel_ratio_min_input, self.channel_ratio_max_input]:
            input_widget.setStyleSheet(input_style)
        
        return data_filter_group
    
    def test_filter_logic(self):
        """测试过滤逻辑"""
        try:
            # 获取当前设置
            trade_min = int(self.trade_index_min_input.text() or "0")
            trade_max = int(self.trade_index_max_input.text() or "999999")
            ratio_min = float(self.channel_ratio_min_input.text() or "0")
            ratio_max = float(self.channel_ratio_max_input.text() or "100")
            
            print(f"\n=== 过滤设置测试 ===")
            print(f"成交指数区间: {trade_min} ~ {trade_max}")
            print(f"渠道占比区间: {ratio_min}% ~ {ratio_max}%")
            
            # 模拟测试数据
            test_data = [
                {"成交指数": 5000, "渠道占比": "95.50%", "商品": "商品A"},
                {"成交指数": 3000, "渠道占比": "88.20%", "商品": "商品B"},
                {"成交指数": 1800, "渠道占比": "92.00%", "商品": "商品C"},
                {"成交指数": 2500, "渠道占比": "75.30%", "商品": "商品D"},
            ]
            
            print(f"\n成交指数过滤结果:")
            for item in test_data:
                if item["成交指数"] >= trade_min:
                    print(f"  ✓ {item['商品']}: 成交指数 {item['成交指数']} (保留)")
                else:
                    print(f"  ✗ {item['商品']}: 成交指数 {item['成交指数']} (停止采集)")
                    break
            
            print(f"\n渠道占比过滤结果:")
            for item in test_data:
                ratio = float(item["渠道占比"].replace("%", ""))
                if ratio_min <= ratio <= ratio_max:
                    print(f"  ✓ {item['商品']}: 渠道占比 {item['渠道占比']} (显示)")
                else:
                    print(f"  ✗ {item['商品']}: 渠道占比 {item['渠道占比']} (隐藏)")
            
        except ValueError as e:
            print(f"输入错误: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 更新设置
            self.filter_settings.update({
                "成交指数最小值": int(self.trade_index_min_input.text() or "0"),
                "成交指数最大值": int(self.trade_index_max_input.text() or "999999"),
                "渠道占比最小值": float(self.channel_ratio_min_input.text() or "0"),
                "渠道占比最大值": float(self.channel_ratio_max_input.text() or "100")
            })
            
            # 保存到文件
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)
            
            settings_file = data_dir / "demo_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.filter_settings, f, ensure_ascii=False, indent=2)
            
            print(f"\n设置已保存到: {settings_file}")
            print("设置内容:")
            for key, value in self.filter_settings.items():
                print(f"  {key}: {value}")
                
        except Exception as e:
            print(f"保存失败: {e}")
    
    def load_settings(self):
        """加载设置"""
        try:
            settings_file = Path("data") / "demo_settings.json"
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.filter_settings.update(saved_settings)
                    
                # 更新界面
                self.trade_index_min_input.setText(str(self.filter_settings["成交指数最小值"]))
                self.trade_index_max_input.setText(str(self.filter_settings["成交指数最大值"]))
                self.channel_ratio_min_input.setText(str(self.filter_settings["渠道占比最小值"]))
                self.channel_ratio_max_input.setText(str(self.filter_settings["渠道占比最大值"]))
                
                print(f"已从文件加载设置: {settings_file}")
        except Exception as e:
            print(f"加载设置失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("数据过滤设置演示")
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建主窗口
    window = FilterSettingsDemo()
    window.show()
    
    print("=== 数据过滤设置演示程序 ===")
    print("1. 修改输入框中的数值")
    print("2. 点击'测试过滤逻辑'查看过滤效果")
    print("3. 点击'保存设置'保存当前配置")
    print("4. 重启程序会自动加载保存的设置")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
