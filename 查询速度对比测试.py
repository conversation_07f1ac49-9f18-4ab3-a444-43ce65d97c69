#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询速度对比测试 - 展示优化前后的速度差异
"""

import time
import concurrent.futures
from datetime import datetime

def simulate_api_request(delay=0.2):
    """模拟API请求"""
    time.sleep(delay)
    return "success"

def test_old_speed():
    """测试优化前的速度（原始设置）"""
    print("【优化前】原始速度测试")
    print("-" * 50)
    
    # 原始设置：每个请求0.5秒延迟，商品间1秒延迟
    products = 3
    days_per_product = 10
    request_delay = 0.5
    product_delay = 1.0
    
    start_time = time.time()
    
    for product in range(products):
        print(f"正在处理第{product+1}/{products}个商品")
        
        # 查询标题
        simulate_api_request(request_delay)
        
        # 查询每天数据（串行）
        for day in range(days_per_product):
            simulate_api_request(request_delay)
        
        # 商品间延迟
        time.sleep(product_delay)
    
    total_time = time.time() - start_time
    total_requests = products * (days_per_product + 1)
    
    print(f"总耗时: {total_time:.1f}秒")
    print(f"总请求: {total_requests}个")
    print(f"平均速度: {total_requests/total_time:.2f}个/秒")
    print()
    
    return total_time

def test_new_speed_mode(mode_name, concurrent_workers, request_delay, product_delay):
    """测试新的速度模式"""
    print(f"【{mode_name}】速度测试")
    print("-" * 50)
    
    products = 3
    days_per_product = 10
    
    start_time = time.time()
    
    for product in range(products):
        print(f"正在处理第{product+1}/{products}个商品")
        
        # 查询标题
        simulate_api_request(request_delay)
        
        # 并发查询每天数据
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_workers) as executor:
            futures = [executor.submit(simulate_api_request, request_delay) for _ in range(days_per_product)]
            for future in concurrent.futures.as_completed(futures):
                future.result()
        
        # 商品间延迟
        time.sleep(product_delay)
    
    total_time = time.time() - start_time
    total_requests = products * (days_per_product + 1)
    
    print(f"总耗时: {total_time:.1f}秒")
    print(f"总请求: {total_requests}个")
    print(f"平均速度: {total_requests/total_time:.2f}个/秒")
    print(f"并发数: {concurrent_workers}")
    print(f"请求延迟: {request_delay*1000:.0f}毫秒")
    print(f"商品延迟: {product_delay*1000:.0f}毫秒")
    print()
    
    return total_time

def test_batch_mode():
    """测试批量模式"""
    print("【批量模式】速度测试")
    print("-" * 50)
    
    products = 3
    days_per_product = 10
    concurrent_workers = 8
    request_delay = 0.02
    
    start_time = time.time()
    
    # 先批量查询所有标题
    print("批量查询商品标题...")
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_workers) as executor:
        futures = [executor.submit(simulate_api_request, request_delay) for _ in range(products)]
        for future in concurrent.futures.as_completed(futures):
            future.result()
    
    # 按日期批量查询
    for day in range(days_per_product):
        print(f"查询第{day+1}天所有商品数据...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_workers) as executor:
            futures = [executor.submit(simulate_api_request, request_delay) for _ in range(products)]
            for future in concurrent.futures.as_completed(futures):
                future.result()
        time.sleep(0.01)  # 日期间极小延迟
    
    total_time = time.time() - start_time
    total_requests = products * (days_per_product + 1)
    
    print(f"总耗时: {total_time:.1f}秒")
    print(f"总请求: {total_requests}个")
    print(f"平均速度: {total_requests/total_time:.2f}个/秒")
    print(f"并发数: {concurrent_workers}")
    print(f"请求延迟: {request_delay*1000:.0f}毫秒")
    print()
    
    return total_time

def main():
    """主测试函数"""
    print("商品成交量查询速度优化对比测试")
    print("=" * 60)
    print("测试场景: 3个商品 × 10天数据 = 33个请求")
    print("=" * 60)
    print()
    
    # 测试原始速度
    old_time = test_old_speed()
    
    # 测试各种新速度模式
    speed_modes = [
        ("慢速模式", 2, 0.1, 0.3),
        ("普通模式", 5, 0.05, 0.1),
        ("快速模式", 8, 0.02, 0.05),
        ("超快模式", 12, 0.005, 0.01),
    ]
    
    results = []
    
    for mode_name, workers, req_delay, prod_delay in speed_modes:
        new_time = test_new_speed_mode(mode_name, workers, req_delay, prod_delay)
        improvement = old_time / new_time
        results.append((mode_name, new_time, improvement))
    
    # 测试批量模式
    batch_time = test_batch_mode()
    batch_improvement = old_time / batch_time
    results.append(("批量模式", batch_time, batch_improvement))
    
    # 显示对比结果
    print("=" * 60)
    print("速度提升对比结果")
    print("=" * 60)
    print(f"{'模式':<12} {'耗时(秒)':<10} {'提升倍数':<10} {'提升百分比'}")
    print("-" * 60)
    print(f"{'原始模式':<12} {old_time:<10.1f} {'1.0x':<10} {'基准'}")
    
    for mode_name, new_time, improvement in results:
        improvement_percent = f"{(improvement-1)*100:.0f}%"
        print(f"{mode_name:<12} {new_time:<10.1f} {improvement:<10.1f}x {improvement_percent}")
    
    print()
    print("优化效果总结:")
    print(f"• 最大速度提升: {max(result[2] for result in results):.1f}倍")
    print(f"• 推荐日常使用: 快速模式 (提升{results[2][2]:.1f}倍)")
    print(f"• 极限速度: 超快模式 (提升{results[3][2]:.1f}倍)")
    print(f"• 大量数据: 批量模式 (提升{results[4][2]:.1f}倍)")

if __name__ == "__main__":
    main()
