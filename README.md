# 快手采集工具

一个用于采集快手平台数据的桌面应用程序，具有简洁的用户界面和强大的数据处理功能。

## 功能特性

- **简约UI设计**: 采用简洁明了的界面设计，操作直观易懂
- **优化交互体验**: 下拉框选项立即全部显示，无逐个加载延迟
- **智能Cookie检测**: 实时检测Cookie状态，自动启用相关功能
- **多条件筛选**: 支持日期、类目、渠道等多维度数据筛选界面
- **自动登录**: 集成Cookie管理，支持自动登录快手平台
- **类目解析**: 自动解析类目数据，支持四级类目筛选
- **数据采集**: 完整的数据采集功能，支持实时采集快手平台数据
- **数据导出**: 支持将采集数据导出为Excel格式

## 系统要求

- Python 3.7+
- Windows 10/11
- PyQt5 或 PyQt6
- requests 库

## 安装说明

1. **安装Python依赖**:
   ```bash
   pip install PyQt5 requests pandas openpyxl
   ```
   或者使用PyQt6:
   ```bash
   pip install PyQt6 requests pandas openpyxl
   ```

2. **下载项目文件**:
   确保以下文件在同一目录下:
   - `main.py` - 主程序
   - `cookie_exporter.py` - Cookie管理
   - `category_parser.py` - 类目解析
   - `data_collector.py` - 数据采集
   - `类目响应数据.md` - 类目数据文件
   - `run.py` - 启动脚本

## 使用方法

### 1. 启动程序
```bash
python run.py
```
或直接运行:
```bash
python main.py
```

### 2. 首次使用
1. **解析类目**: 点击"解析类目"按钮，解析类目数据文件
2. **登录**: 点击"登录"按钮，在弹出的浏览器窗口中登录快手账号
3. **等待**: 登录完成后，程序会自动检测Cookie状态

### 3. 功能说明
- **解析类目**: 随时可用，解析本地类目数据文件
- **登录**: 启动Cookie采集程序
- **开始采集**: 根据筛选条件开始数据采集
- **停止采集**: 停止正在进行的数据采集
- **导出数据**: 将采集到的数据导出为Excel文件

### 4. 注意事项
- 首次使用必须先解析类目数据和登录获取Cookie
- 采集过程中请保持网络连接稳定
- 采集到的数据仅供参考，请以官方数据为准

## 界面说明

### 上半部分 - 筛选设置区域
- **日期筛选**: 提供52周的日期选项
- **类目筛选**: 四级类目级联选择
- **渠道筛选**: 直播间、短视频、商品卡等
- **功能按钮**: 登录、开始、停止、导出、解析类目

### 下半部分 - 数据展示区域
- **状态显示**: 显示当前操作状态
- **数据表格**: 展示采集到的数据，包含以下字段:
  - 排名、标题、链接
  - 销售件数、客单价
  - 成交指数、总成交指数
  - 渠道占比、分销销量
  - 达人数、类目

## 文件结构

```
快手采集/
├── main.py                 # 主程序文件
├── cookie_exporter.py      # Cookie管理模块
├── category_parser.py      # 类目解析模块
├── data_collector.py       # 数据采集模块
├── run.py                  # 启动脚本
├── 类目响应数据.md         # 类目数据源文件
├── data/                   # 数据目录
│   ├── cookies.txt         # Cookie文件1
│   ├── cookies2.txt        # Cookie文件2
│   └── Category data.txt   # 解析后的类目数据
└── README.md               # 说明文档
```

## 注意事项

1. **首次使用**: 必须先解析类目数据和登录账号
2. **网络连接**: 确保网络连接正常，能够访问快手平台
3. **Cookie有效期**: Cookie可能会过期，需要重新登录
4. **数据准确性**: 采集的数据仅供参考，请以官方数据为准

## 故障排除

### 常见问题

1. **程序无法启动**:
   - 检查Python版本是否为3.7+
   - 确认已安装所需依赖包

2. **登录失败**:
   - 检查网络连接
   - 尝试重新登录
   - 清除浏览器缓存

3. **数据采集失败**:
   - 确认已成功登录
   - 检查筛选条件是否正确
   - 重新登录获取新的Cookie

4. **类目解析失败**:
   - 确认`类目响应数据.md`文件存在
   - 检查文件格式是否正确

### 技术支持

如遇到其他问题，请检查:
1. 控制台错误信息
2. 网络连接状态
3. 文件权限设置

## 版本信息

- 版本: 1.0.0
- 更新日期: 2024-01-20
- 兼容性: Windows 10/11, Python 3.7+

## 免责声明

本工具仅供学习和研究使用，请遵守相关平台的使用条款和法律法规。使用本工具产生的任何后果由用户自行承担。
