#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类目解析模块
解析类目响应数据.md文件，生成树形结构的Category data.txt文件
"""

import json
from pathlib import Path


class CategoryParser:
    """类目解析器"""

    def __init__(self):
        self.category_tree = {}
        self.category_mapping = {}  # 存储key到名称的映射
        
    def parse_category_file(self, file_path):
        """解析类目文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析JSON数据
            data = json.loads(content)
            
            # 提取类目数据
            if 'data' in data and 'sytWebItemTopRank' in data['data']:
                for item in data['data']['sytWebItemTopRank']:
                    if item.get('code') == 'itemCategory' and 'list' in item:
                        self.parse_category_list(item['list'])
                        
            return True
            
        except Exception as e:
            print(f"解析类目文件失败: {e}")
            return False
            
    def parse_category_list(self, category_list):
        """解析类目列表"""
        for category in category_list:
            if category.get('hierarchy') == 0:  # 一级类目
                first_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': category.get('categoryPid', ''),
                    'children': {}
                }
                self.category_tree[category['label']] = first_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_second_level(category['children'], category['label'], category['key'])

    def parse_second_level(self, children, first_level, first_key):
        """解析二级类目"""
        for category in children:
            if category.get('hierarchy') == 1:  # 二级类目
                second_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': first_level,
                    'children': {}
                }
                self.category_tree[first_level]['children'][category['label']] = second_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_third_level(category['children'], first_level, category['label'], category['key'])

    def parse_third_level(self, children, first_level, second_level, second_key):
        """解析三级类目"""
        for category in children:
            if category.get('hierarchy') == 2:  # 三级类目
                third_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': second_key,
                    'children': []
                }
                self.category_tree[first_level]['children'][second_level]['children'][category['label']] = third_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_fourth_level(category['children'], first_level, second_level, category['label'], category['key'])

    def parse_fourth_level(self, children, first_level, second_level, third_level, third_key):
        """解析四级类目"""
        for category in children:
            if category.get('hierarchy') == 3:  # 四级类目
                fourth_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': third_key
                }
                self.category_tree[first_level]['children'][second_level]['children'][third_level]['children'].append(fourth_level_data)
                self.category_mapping[category['key']] = category['label']
                
    def save_category_data(self, output_path):
        """保存类目数据到文件"""
        try:
            # 确保data目录存在
            output_path.parent.mkdir(exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.category_tree, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"保存类目数据失败: {e}")
            return False
            
    def print_tree_structure(self):
        """打印树形结构（用于调试）"""
        for first_level, first_data in self.category_tree.items():
            print(f"{first_level}（一级类目 | categoryPid: {first_data['categoryPid']}）")

            for second_level, second_data in first_data['children'].items():
                print(f"└── {second_level}（二级类目 | categoryPid: \"{second_data['categoryPid']}\"）")

                for third_level, third_data in second_data['children'].items():
                    print(f"  └── {third_level}（三级类目 | categoryPid: \"{third_data['categoryPid']}\"）")

                    for fourth_data in third_data['children']:
                        print(f"    ├── {fourth_data['name']}（key: \"{fourth_data['key']}\"）")

    def print_tree_structure_sample(self):
        """打印树形结构示例（只显示第一个分支）"""
        if not self.category_tree:
            return

        # 只显示第一个一级类目的完整结构
        first_level_name = list(self.category_tree.keys())[0]
        first_data = self.category_tree[first_level_name]

        print(f"{first_level_name}（一级类目 | categoryPid: {first_data['categoryPid']}）")

        # 只显示第一个二级类目
        if first_data['children']:
            second_level_name = list(first_data['children'].keys())[0]
            second_data = first_data['children'][second_level_name]

            print(f"└── {second_level_name}（二级类目 | categoryPid: \"{second_data['categoryPid']}\"）")

            # 只显示第一个三级类目
            if second_data['children']:
                third_level_name = list(second_data['children'].keys())[0]
                third_data = second_data['children'][third_level_name]

                print(f"  └── {third_level_name}（三级类目 | categoryPid: \"{third_data['categoryPid']}\"）")

                # 显示前几个四级类目
                for i, fourth_data in enumerate(third_data['children'][:3]):
                    symbol = "├──" if i < 2 else "└──"
                    print(f"    {symbol} {fourth_data['name']}（key: \"{fourth_data['key']}\"）")

                if len(third_data['children']) > 3:
                    print(f"    └── ... 还有 {len(third_data['children']) - 3} 个四级类目")
                        
    def get_category_count(self):
        """获取类目统计信息"""
        first_count = len(self.category_tree)
        second_count = sum(len(first_data['children']) for first_data in self.category_tree.values())
        third_count = sum(
            len(second_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
        )
        fourth_count = sum(
            len(third_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
            for third_data in second_data['children'].values()
        )

        return {
            'first_level': first_count,
            'second_level': second_count,
            'third_level': third_count,
            'fourth_level': fourth_count
        }


def parse_categories():
    """解析类目的主函数"""
    parser = CategoryParser()
    
    # 输入文件路径
    input_file = Path("类目响应数据.md")
    
    # 输出文件路径
    output_dir = Path("data")
    output_file = output_dir / "Category data.txt"
    
    if not input_file.exists():
        print(f"错误：未找到文件 {input_file}")
        return False
        
    print("开始解析类目数据...")
    
    # 解析文件
    if not parser.parse_category_file(input_file):
        print("解析失败")
        return False
        
    # 保存数据
    if not parser.save_category_data(output_file):
        print("保存失败")
        return False
        
    # 获取统计信息
    stats = parser.get_category_count()
    
    print(f"解析完成！")
    print(f"一级类目: {stats['first_level']} 个")
    print(f"二级类目: {stats['second_level']} 个")
    print(f"三级类目: {stats['third_level']} 个")
    print(f"四级类目: {stats['fourth_level']} 个")
    print(f"数据已保存到: {output_file}")

    # 打印部分树形结构示例
    print("\n树形结构示例:")
    parser.print_tree_structure_sample()

    return True


if __name__ == "__main__":
    parse_categories()
