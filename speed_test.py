#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询速度测试脚本
"""

import time
import concurrent.futures
from datetime import datetime

def simulate_api_request(delay=0.2):
    """模拟API请求"""
    time.sleep(delay)
    return f"结果_{time.time()}"

def test_serial_requests(num_requests, delay=0.2):
    """测试串行请求"""
    print(f"\n串行请求测试 - {num_requests}个请求，每个延迟{delay}秒")
    start_time = time.time()
    
    results = []
    for i in range(num_requests):
        result = simulate_api_request(delay)
        results.append(result)
        print(f"完成请求 {i+1}/{num_requests}")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"串行总耗时: {total_time:.2f}秒")
    return total_time

def test_concurrent_requests(num_requests, max_workers=3, delay=0.2):
    """测试并发请求"""
    print(f"\n并发请求测试 - {num_requests}个请求，{max_workers}个并发，每个延迟{delay}秒")
    start_time = time.time()
    
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = [executor.submit(simulate_api_request, delay) for _ in range(num_requests)]
        
        # 收集结果
        for i, future in enumerate(concurrent.futures.as_completed(futures)):
            result = future.result()
            results.append(result)
            print(f"完成请求 {i+1}/{num_requests}")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"并发总耗时: {total_time:.2f}秒")
    return total_time

def test_batch_by_date(num_products, num_days, max_workers=3, delay=0.2):
    """测试按日期批量处理"""
    print(f"\n按日期批量测试 - {num_products}个商品，{num_days}天，{max_workers}个并发")
    start_time = time.time()
    
    total_requests = 0
    
    # 先查询所有商品标题（串行）
    print("查询商品标题...")
    for i in range(num_products):
        simulate_api_request(delay)
        total_requests += 1
        print(f"标题查询 {i+1}/{num_products}")
    
    # 按日期批量查询成交量（并发）
    for day in range(num_days):
        print(f"查询第{day+1}天数据...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(simulate_api_request, delay) for _ in range(num_products)]
            
            for future in concurrent.futures.as_completed(futures):
                future.result()
                total_requests += 1
        
        print(f"第{day+1}天完成")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"批量处理总耗时: {total_time:.2f}秒，总请求数: {total_requests}")
    return total_time

def compare_speed_modes():
    """比较不同速度模式的性能"""
    print("=" * 60)
    print("查询速度模式性能比较")
    print("=" * 60)
    
    # 测试参数
    num_products = 5
    num_days = 30
    request_delay = 0.2
    
    print(f"测试场景: {num_products}个商品，{num_days}天数据")
    print(f"单个请求延迟: {request_delay}秒")
    
    # 慢速模式（完全串行）
    print("\n【慢速模式】- 完全串行")
    slow_time = test_serial_requests(num_products * (num_days + 1), request_delay)
    
    # 普通模式（商品串行，日期串行）
    print("\n【普通模式】- 商品串行，日期串行")
    normal_start = time.time()
    for product in range(num_products):
        print(f"处理商品 {product+1}/{num_products}")
        # 标题查询
        simulate_api_request(request_delay)
        # 日期查询
        for day in range(num_days):
            simulate_api_request(request_delay)
        time.sleep(0.5)  # 商品间延迟
    normal_time = time.time() - normal_start
    print(f"普通模式总耗时: {normal_time:.2f}秒")
    
    # 快速模式（按日期批量）
    print("\n【快速模式】- 按日期批量处理")
    fast_time = test_batch_by_date(num_products, num_days, max_workers=3, delay=request_delay)
    
    # 超快模式（更高并发）
    print("\n【超快模式】- 高并发批量处理")
    ultra_fast_time = test_batch_by_date(num_products, num_days, max_workers=5, delay=request_delay)
    
    # 性能对比
    print("\n" + "=" * 60)
    print("性能对比结果:")
    print("=" * 60)
    print(f"慢速模式:   {slow_time:.2f}秒 (基准)")
    print(f"普通模式:   {normal_time:.2f}秒 (提升 {slow_time/normal_time:.1f}x)")
    print(f"快速模式:   {fast_time:.2f}秒 (提升 {slow_time/fast_time:.1f}x)")
    print(f"超快模式:   {ultra_fast_time:.2f}秒 (提升 {slow_time/ultra_fast_time:.1f}x)")
    
    print(f"\n推荐设置:")
    print(f"- 少量商品(≤3个): 普通模式")
    print(f"- 中等商品(4-10个): 快速模式")
    print(f"- 大量商品(>10个): 超快模式")

if __name__ == "__main__":
    compare_speed_modes()
