#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品成交量查询功能测试
"""

import sys
import re
from datetime import datetime, timedelta

def test_extract_product_id():
    """测试商品ID提取功能"""
    test_links = [
        "https://app.kwaixiaodian.com/merchant/shop/detail?id=24803176646578&hyId=kwaishop&layoutType=4",
        "https://app.kwaixiaodian.com/merchant/shop/detail?id=24903052364858&hyId=kwaishop&layoutType=4",
        "https://app.kwaixiaodian.com/merchant/shop/detail?id=12345678901234&hyId=kwaishop&layoutType=4"
    ]
    
    print("测试商品ID提取功能:")
    for link in test_links:
        match = re.search(r'id=(\d+)', link)
        if match:
            product_id = match.group(1)
            print(f"链接: {link}")
            print(f"提取的ID: {product_id}")
            print("-" * 50)
        else:
            print(f"无法提取ID: {link}")

def test_date_generation():
    """测试日期生成功能"""
    print("\n测试日期生成功能:")
    
    for days in [30, 60, 90]:
        print(f"\n{days}天日期列表:")
        dates = []
        today = datetime.now()
        start_date = today - timedelta(days=1)  # 从昨天开始
        
        for i in range(days):
            date = start_date - timedelta(days=i)
            dates.append(date.strftime('%Y-%m-%d'))
        
        # 只显示前5个和后5个日期
        print(f"前5个日期: {dates[:5]}")
        print(f"后5个日期: {dates[-5:]}")
        print(f"总共: {len(dates)} 个日期")

def test_table_headers():
    """测试表格表头生成"""
    print("\n测试表格表头生成:")
    
    for days in [30, 60, 90]:
        print(f"\n{days}天表头:")
        headers = ["商品标题", "商品链接"]
        
        # 生成日期列（从昨天开始向前推算）
        today = datetime.now()
        start_date = today - timedelta(days=1)  # 从昨天开始
        
        for i in range(days):
            date = start_date - timedelta(days=i)
            date_str = date.strftime("%m/%d")
            headers.append(date_str)
        
        print(f"表头数量: {len(headers)}")
        print(f"前10个表头: {headers[:10]}")
        print(f"后5个表头: {headers[-5:]}")

def test_payload_generation():
    """测试请求载荷生成"""
    print("\n测试请求载荷生成:")
    
    product_id = "24903052364858"
    date = "2025-07-28"
    
    # 查询标题的载荷
    title_payload = {
        "itemId": product_id,
        "timeRange": "CUSTOMIZED_DAY",
        "currentStartDay": date,
        "currentEndDay": date
    }
    
    # 查询成交量的载荷
    sales_payload = {
        "module": "sytWebItemTopRank4Seller",
        "pageNum": 1,
        "pageSize": 10,
        "timeRange": "CUSTOMIZED_DAY",
        "currentStartDay": date,
        "currentEndDay": date,
        "param": [{"code": "itemId", "value": [product_id]}]
    }
    
    print("查询标题载荷:")
    print(title_payload)
    print("\n查询成交量载荷:")
    print(sales_payload)

if __name__ == "__main__":
    print("商品成交量查询功能测试")
    print("=" * 60)
    
    test_extract_product_id()
    test_date_generation()
    test_table_headers()
    test_payload_generation()
    
    print("\n测试完成!")
