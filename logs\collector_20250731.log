2025-07-31 00:55:32,902 - INFO - DataCollector资源清理完成
2025-07-31 00:57:07,527 - INFO - DataCollector资源清理完成
2025-07-31 01:06:10,998 - INFO - DataCollector资源清理完成
2025-07-31 01:07:24,137 - INFO - DataCollector资源清理完成
2025-07-31 01:13:33,952 - INFO - DataCollector资源清理完成
2025-07-31 01:17:20,574 - INFO - DataCollector资源清理完成
2025-07-31 01:23:05,854 - INFO - DataCollector资源清理完成
2025-07-31 01:23:20,513 - INFO - DataCollector资源清理完成
2025-07-31 01:23:21,743 - INFO - DataCollector资源清理完成
2025-07-31 01:31:30,985 - INFO - DataCollector资源清理完成
2025-07-31 01:31:38,568 - INFO - DataCollector资源清理完成
2025-07-31 01:35:03,296 - INFO - DataCollector资源清理完成
2025-07-31 01:35:07,513 - INFO - DataCollector资源清理完成
2025-07-31 01:35:14,707 - INFO - DataCollector资源清理完成
2025-07-31 01:37:26,115 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-31 01:37:26,116 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-07-31 01:37:26,131 - INFO - 类目数据加载成功
2025-07-31 01:37:26,135 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-31 01:37:26,138 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:27,730 - INFO - 成功获取 100 条基础数据
2025-07-31 01:37:27,730 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-07-31 01:37:27,735 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:27,736 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:27,736 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:28,726 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:28,735 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:28,807 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:29,655 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:29,663 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:29,775 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:30,547 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:30,561 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:30,718 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:31,468 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:31,469 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:34,283 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-31 01:37:34,285 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-07-31 01:37:34,299 - INFO - 类目数据加载成功
2025-07-31 01:37:34,300 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-31 01:37:34,304 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:34,948 - INFO - 成功获取 100 条基础数据
2025-07-31 01:37:34,949 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-07-31 01:37:34,952 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:34,954 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:34,958 - INFO - 成功加载 9 个Cookie
2025-07-31 01:37:38,478 - INFO - DataCollector资源清理完成
2025-07-31 01:41:23,843 - INFO - DataCollector资源清理完成
2025-07-31 01:52:34,024 - INFO - DataCollector资源清理完成
2025-07-31 01:53:06,021 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-31 01:53:06,022 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-07-31 01:53:06,037 - INFO - 类目数据加载成功
2025-07-31 01:53:06,041 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-31 01:53:06,043 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:06,976 - INFO - 成功获取 100 条基础数据
2025-07-31 01:53:06,976 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-07-31 01:53:06,981 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:06,981 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:06,982 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:07,773 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:07,796 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:07,796 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:08,570 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:08,576 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:08,588 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:09,523 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:09,524 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:09,567 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:10,426 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:10,433 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:10,526 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:11,323 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:11,371 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:11,403 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:12,268 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:12,276 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:12,297 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:13,166 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:13,186 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:13,199 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:14,097 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:14,097 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:14,116 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:15,023 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:15,033 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:15,033 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:15,967 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:15,967 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:16,002 - INFO - 成功加载 9 个Cookie
2025-07-31 01:53:16,013 - INFO - 正在关闭线程池...
2025-07-31 01:53:16,872 - INFO - DataCollector资源清理完成
2025-07-31 02:23:06,893 - INFO - DataCollector资源清理完成
2025-07-31 02:24:12,671 - INFO - DataCollector资源清理完成
2025-07-31 02:24:36,950 - INFO - DataCollector资源清理完成
2025-07-31 02:29:04,542 - INFO - DataCollector资源清理完成
2025-07-31 02:29:18,037 - INFO - DataCollector资源清理完成
2025-07-31 02:30:16,581 - INFO - DataCollector资源清理完成
2025-07-31 02:30:36,877 - INFO - DataCollector资源清理完成
2025-07-31 02:32:40,437 - INFO - DataCollector资源清理完成
2025-07-31 02:42:06,460 - INFO - DataCollector资源清理完成
2025-07-31 02:42:30,949 - INFO - DataCollector资源清理完成
2025-07-31 02:42:46,250 - INFO - DataCollector资源清理完成
2025-07-31 02:45:52,677 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '生鲜食品行业', '二级类目': '零食/坚果/特产', '三级类目': '肉干肉脯/熏腊卤味/肉类熟食', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-31 02:45:52,678 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-07-31 02:45:52,693 - INFO - 类目数据加载成功
2025-07-31 02:45:52,698 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["生鲜食品行业"]}, {"code": "categoryLevel1Id", "value": ["1164"]}, {"code": "categoryLevel2Id", "value": ["8275"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-31 02:45:52,700 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:53,593 - INFO - 成功获取 100 条基础数据
2025-07-31 02:45:53,593 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-07-31 02:45:53,596 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:53,596 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:53,601 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:54,524 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:54,539 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:54,539 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:55,427 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:55,441 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:55,462 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:56,337 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:56,375 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:56,401 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:57,270 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:57,279 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:57,306 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:58,205 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:58,221 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:58,245 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:59,127 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:59,168 - INFO - 成功加载 9 个Cookie
2025-07-31 02:45:59,208 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:00,062 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:00,102 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:00,241 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:00,932 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:00,966 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:01,068 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:01,829 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:01,904 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:01,930 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:02,700 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:02,786 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:02,816 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:03,569 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:03,685 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:03,700 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:04,450 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:04,571 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:04,634 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:05,327 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:05,478 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:05,515 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:06,187 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:06,357 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:06,375 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:07,034 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:07,213 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:07,235 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:07,891 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:08,086 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:08,121 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:08,721 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:08,917 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:08,998 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:09,528 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:09,801 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:09,849 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:10,304 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:10,666 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:10,696 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:11,117 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:11,522 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:11,555 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:11,881 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:12,434 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:12,469 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:12,681 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:13,306 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:13,324 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:13,509 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:14,196 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:14,204 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:14,374 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:15,059 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:15,091 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:15,221 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:15,983 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:16,020 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:16,113 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:16,949 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:16,970 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:17,018 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:17,865 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:17,889 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:17,937 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:18,777 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:18,788 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:18,896 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:19,664 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:19,689 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:19,768 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:20,548 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:20,600 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:20,638 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:21,466 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:21,503 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:21,558 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:22,346 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:22,398 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:22,516 - INFO - 成功加载 9 个Cookie
2025-07-31 02:46:31,659 - INFO - DataCollector资源清理完成
2025-07-31 02:47:02,692 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-31 02:47:02,693 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-07-31 02:47:02,719 - INFO - 类目数据加载成功
2025-07-31 02:47:02,725 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-31 02:47:02,726 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:04,277 - INFO - 成功获取 100 条基础数据
2025-07-31 02:47:04,278 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-07-31 02:47:04,282 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:04,282 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:04,283 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:05,242 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:05,256 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:05,257 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:06,240 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:06,240 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:06,321 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:07,230 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:07,255 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:07,303 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:08,177 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:08,188 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:08,320 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:09,123 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:09,130 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:09,296 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:09,983 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:10,066 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:10,200 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:10,846 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:11,055 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:11,153 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:11,709 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:11,977 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:12,051 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:12,521 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:12,834 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:12,910 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:13,342 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:13,733 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:13,788 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:14,126 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:14,605 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:14,684 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:14,955 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:15,493 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:15,538 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:15,823 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:16,369 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:16,428 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:16,727 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:17,327 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:17,413 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:17,599 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:18,257 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:18,398 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:18,542 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:19,163 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:19,308 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:19,524 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:20,087 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:20,185 - INFO - 成功加载 9 个Cookie
2025-07-31 02:47:20,367 - INFO - 成功加载 9 个Cookie
2025-07-31 02:48:44,549 - INFO - DataCollector资源清理完成
2025-07-31 03:27:23,950 - INFO - 设置筛选条件: {'日期': '2025-07-21-2025-07-27', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '商品卡', '售卖形式': '自卖', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-31 03:27:23,951 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-07-31 03:27:23,964 - INFO - 类目数据加载成功
2025-07-31 03:27:23,967 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-21", "currentEndDay": "2025-07-27", "compareStartDay": "2025-07-14", "compareEndDay": "2025-07-20", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["否"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-31 03:27:23,969 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:25,210 - INFO - 成功获取 100 条基础数据
2025-07-31 03:27:25,210 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-07-31 03:27:25,215 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:25,215 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:25,215 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:26,208 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:26,230 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:26,245 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:27,188 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:27,233 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:27,247 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:28,197 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:28,230 - INFO - 成功加载 9 个Cookie
2025-07-31 03:27:28,248 - INFO - 成功加载 9 个Cookie
2025-07-31 03:29:04,337 - INFO - DataCollector资源清理完成
2025-07-31 17:35:32,046 - INFO - DataCollector资源清理完成
2025-07-31 17:35:32,047 - INFO - ProductQueryCollector资源清理完成
2025-07-31 21:33:50,822 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-31 21:33:50,823 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-07-31 21:33:50,837 - INFO - 类目数据加载成功
2025-07-31 21:33:50,841 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-31 21:33:50,844 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:52,916 - INFO - 成功获取 100 条基础数据
2025-07-31 21:33:52,917 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-07-31 21:33:52,926 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:52,926 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:52,928 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:53,929 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:53,960 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:53,979 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:54,892 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:54,924 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:54,931 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:55,887 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:55,888 - INFO - 成功加载 9 个Cookie
2025-07-31 21:33:55,909 - INFO - 成功加载 9 个Cookie
2025-07-31 21:34:50,764 - INFO - 设置商品链接列表，共 9 个链接
2025-07-31 21:34:53,868 - INFO - 设置查询时间范围：30 天
2025-07-31 21:35:24,051 - INFO - DataCollector资源清理完成
2025-07-31 21:35:24,053 - INFO - ProductQueryCollector资源清理完成
2025-07-31 21:35:38,969 - INFO - DataCollector资源清理完成
2025-07-31 21:49:53,255 - INFO - DataCollector资源清理完成
2025-08-01 00:03:20,042 - INFO - DataCollector资源清理完成
