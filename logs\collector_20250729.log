2025-07-29 23:21:17,205 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-29 23:21:17,223 - INFO - 类目数据加载成功
2025-07-29 23:21:17,227 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-29 23:21:17,230 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:18,476 - INFO - 成功获取 100 条基础数据
2025-07-29 23:21:18,479 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:18,479 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:18,483 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:19,383 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:19,519 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:19,585 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:20,433 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:20,533 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:20,667 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:21,391 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:21,565 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:21,631 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:22,328 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:22,581 - INFO - 正在关闭线程池...
2025-07-29 23:21:22,591 - INFO - DataCollector资源清理完成
2025-07-29 23:21:43,973 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-29 23:21:43,990 - INFO - 类目数据加载成功
2025-07-29 23:21:43,994 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-29 23:21:43,999 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:44,783 - INFO - 成功获取 100 条基础数据
2025-07-29 23:21:44,786 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:44,791 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:44,793 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:46,166 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:46,238 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:46,262 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:47,546 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:47,579 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:47,602 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:49,458 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:49,464 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:49,464 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:50,367 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:50,378 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:50,390 - INFO - 成功加载 9 个Cookie
2025-07-29 23:21:50,434 - INFO - 正在关闭线程池...
2025-07-29 23:21:51,437 - INFO - DataCollector资源清理完成
2025-07-29 23:25:15,684 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-29 23:25:15,703 - INFO - 类目数据加载成功
2025-07-29 23:25:15,714 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-29 23:25:15,721 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:16,438 - INFO - 成功获取 100 条基础数据
2025-07-29 23:25:16,442 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:16,443 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:16,443 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:17,462 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:17,496 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:17,632 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:18,839 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:18,886 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:18,916 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:19,775 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:19,813 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:19,856 - INFO - 成功加载 9 个Cookie
2025-07-29 23:25:20,594 - INFO - 正在关闭线程池...
2025-07-29 23:25:20,656 - INFO - DataCollector资源清理完成
2025-07-29 23:25:23,830 - INFO - DataCollector资源清理完成
2025-07-29 23:26:44,799 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-29 23:26:44,824 - INFO - 类目数据加载成功
2025-07-29 23:26:44,827 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-29 23:26:44,829 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:45,711 - INFO - 成功获取 100 条基础数据
2025-07-29 23:26:45,815 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:45,887 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:45,941 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:47,745 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:47,764 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:47,769 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:48,681 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:48,688 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:48,711 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:49,569 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:49,607 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:49,759 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:50,553 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:50,576 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:50,663 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:51,494 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:51,515 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:51,550 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:52,529 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:52,549 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:52,612 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:53,475 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:53,536 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:53,846 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:54,527 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:54,566 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:54,875 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:55,351 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:56,156 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:56,160 - INFO - 成功加载 9 个Cookie
2025-07-29 23:26:56,529 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:00,359 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:00,367 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:00,491 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:01,408 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:01,436 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:01,538 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:02,467 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:02,477 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:02,559 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:03,491 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:03,527 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:03,587 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:04,573 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:04,590 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:04,637 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:05,568 - INFO - 正在关闭线程池...
2025-07-29 23:27:05,598 - INFO - DataCollector资源清理完成
2025-07-29 23:27:16,799 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '直播间', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-29 23:27:16,816 - INFO - 类目数据加载成功
2025-07-29 23:27:16,819 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["直播间"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-29 23:27:16,821 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:17,893 - INFO - 成功获取 100 条基础数据
2025-07-29 23:27:17,897 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:17,898 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:17,898 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:18,801 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:18,801 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:18,852 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:19,715 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:19,720 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:19,736 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:20,624 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:20,663 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:20,672 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:21,520 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:21,547 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:21,557 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:22,429 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:22,440 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:22,453 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:23,324 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:23,325 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:23,338 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:24,177 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:24,236 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:24,254 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:25,111 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:25,262 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:25,268 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:26,067 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:26,139 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:26,194 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:27,010 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:27,034 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:27,060 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:28,018 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:28,097 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:28,113 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:28,858 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:29,278 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:29,283 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:29,711 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:30,266 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:30,276 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:30,670 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:31,285 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:31,305 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:31,598 - INFO - 成功加载 9 个Cookie
2025-07-29 23:27:32,154 - INFO - 正在关闭线程池...
2025-07-29 23:27:32,293 - INFO - DataCollector资源清理完成
