#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品成交量查询模块
"""

import json
import requests
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional
try:
    from PyQt5.QtCore import QThread, pyqtSignal
except ImportError:
    from PyQt6.QtCore import QThread, pyqtSignal
import time
import re
import concurrent.futures
from threading import Lock


class ProductQueryWorker(QThread):
    """商品成交量查询工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(str)  # 进度更新
    title_received = pyqtSignal(int, str, str)  # 商品标题接收 (row, title, link)
    sales_data_received = pyqtSignal(int, int, int)  # 成交量数据接收 (row, date_index, sales_count)
    error_occurred = pyqtSignal(str)  # 错误发生
    query_finished = pyqtSignal()  # 查询完成
    
    def __init__(self):
        super().__init__()
        self.product_links = []
        self.date_range_days = 30
        self.is_running = False
        self.should_stop = False

        # 速度控制设置
        self.speed_mode = "normal"  # fast, normal, slow
        self.max_concurrent_requests = 3  # 最大并发请求数
        self.request_delay = 0.2  # 请求间延迟（秒）
        self.product_delay = 0.5  # 商品间延迟（秒）

        # 线程安全锁
        self.lock = Lock()

        # 设置日志
        self.setup_logging()

        # 加载cookies
        self.cookies = self.load_cookies()

        # 请求头
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'kpf': 'PC_WEB',
            'origin': 'https://syt.kwaixiaodian.com',
            'referer': 'https://syt.kwaixiaodian.com/mobile/seller/datacenter/searchRankDetail',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"product_query_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def load_cookies(self) -> str:
        """加载cookies"""
        try:
            cookies_file = Path("data/cookies.txt")
            if cookies_file.exists():
                with open(cookies_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            return ""
        except Exception as e:
            self.logger.error(f"加载cookies失败: {e}")
            return ""
    
    def set_query_params(self, product_links: List[str], date_range_days: int):
        """设置查询参数"""
        self.product_links = product_links
        self.date_range_days = date_range_days

    def set_speed_mode(self, mode: str):
        """设置查询速度模式"""
        self.speed_mode = mode
        if mode == "ultra_fast":
            self.max_concurrent_requests = 12  # 超高并发
            self.request_delay = 0.005  # 极小延迟：5毫秒
            self.product_delay = 0.01   # 商品间延迟：10毫秒
        elif mode == "fast":
            self.max_concurrent_requests = 8   # 增加并发数
            self.request_delay = 0.02   # 大幅减少延迟：20毫秒
            self.product_delay = 0.05   # 商品间延迟：50毫秒
        elif mode == "normal":
            self.max_concurrent_requests = 5   # 增加并发数
            self.request_delay = 0.05   # 减少延迟：50毫秒
            self.product_delay = 0.1    # 商品间延迟：100毫秒
        elif mode == "slow":
            self.max_concurrent_requests = 2   # 慢速也增加一点并发
            self.request_delay = 0.1    # 减少延迟：100毫秒
            self.product_delay = 0.3    # 商品间延迟：300毫秒
    
    def extract_product_id(self, link: str) -> Optional[str]:
        """从商品链接中提取商品ID"""
        try:
            # 匹配 id=数字 的模式
            match = re.search(r'id=(\d+)', link)
            if match:
                return match.group(1)
            return None
        except Exception as e:
            self.logger.error(f"提取商品ID失败: {e}")
            return None
    
    def generate_date_list(self) -> List[str]:
        """生成日期列表（从昨天开始向前推算）"""
        dates = []
        today = datetime.now()
        start_date = today - timedelta(days=1)  # 从昨天开始
        
        for i in range(self.date_range_days):
            date = start_date - timedelta(days=i)
            dates.append(date.strftime('%Y-%m-%d'))
        
        return dates
    
    def query_product_title(self, product_id: str, date: str) -> Optional[str]:
        """查询商品标题"""
        try:
            url = "https://syt.kwaixiaodian.com/rest/app/gateway/commodity/info"
            
            payload = {
                "itemId": product_id,
                "timeRange": "CUSTOMIZED_DAY",
                "currentStartDay": date,
                "currentEndDay": date
            }
            
            response = requests.post(
                url,
                json=payload,
                headers=self.headers,
                cookies={'cookie': self.cookies},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 200 and 'data' in data:
                    return data['data'].get('itemTitle', '')
            
            return None
            
        except Exception as e:
            self.logger.error(f"查询商品标题失败: {e}")
            return None
    
    def query_sales_data(self, product_id: str, date: str) -> int:
        """查询指定日期的成交量数据"""
        try:
            url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/list"
            
            payload = {
                "module": "sytWebItemTopRank4Seller",
                "pageNum": 1,
                "pageSize": 10,
                "timeRange": "CUSTOMIZED_DAY",
                "currentStartDay": date,
                "currentEndDay": date,
                "param": [{"code": "itemId", "value": [product_id]}]
            }
            
            response = requests.post(
                url,
                json=payload,
                headers=self.headers,
                cookies={'cookie': self.cookies},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 1 and 'data' in data:
                    total = data['data'].get('total', 0)
                    if total == 0:
                        return 0
                    
                    # 计算payOrderCnt数组的总和
                    sales_data = data['data'].get('data', [])
                    total_sales = sum(item.get('payOrderCnt', 0) for item in sales_data)
                    return total_sales
            
            return 0
            
        except Exception as e:
            self.logger.error(f"查询成交量数据失败: {e}")
            return 0
    
    def query_single_date_sales(self, product_id: str, date: str, row: int, date_index: int):
        """查询单个日期的成交量数据（用于并发处理）"""
        try:
            if self.should_stop:
                return

            sales_count = self.query_sales_data(product_id, date)

            # 使用锁确保信号发送的线程安全
            with self.lock:
                if not self.should_stop:
                    self.sales_data_received.emit(row, date_index, sales_count)

            # 并发模式下根据速度模式添加不同延迟
            if self.speed_mode == "ultra_fast":
                time.sleep(0.002)  # 超快模式：2毫秒
            elif self.speed_mode == "fast":
                time.sleep(0.005)  # 快速模式：5毫秒
            elif self.speed_mode == "normal":
                time.sleep(0.01)   # 普通模式：10毫秒
            else:
                time.sleep(self.request_delay)  # 慢速模式保持原延迟

        except Exception as e:
            self.logger.error(f"查询单个日期成交量失败: {e}")

    def run(self):
        """执行查询（优化版本）"""
        try:
            self.is_running = True
            self.should_stop = False

            if not self.cookies:
                self.error_occurred.emit("未找到cookies，请先登录")
                return

            if not self.product_links:
                self.error_occurred.emit("没有要查询的商品链接")
                return

            # 根据速度模式选择执行方式
            if self.speed_mode == "fast" and len(self.product_links) > 3:
                # 快速模式且商品数量较多时使用批量处理
                self.run_batch_mode()
            else:
                # 普通模式使用逐个处理
                self.run_normal_mode()

            self.progress_updated.emit("查询完成")
            self.query_finished.emit()

        except Exception as e:
            self.logger.error(f"查询过程出错: {e}")
            self.error_occurred.emit(f"查询过程出错: {str(e)}")
        finally:
            self.is_running = False

    def run_normal_mode(self):
        """普通模式执行查询"""
        try:
            # 生成日期列表
            date_list = self.generate_date_list()
            total_products = len(self.product_links)

            for row, link in enumerate(self.product_links):
                if self.should_stop:
                    break

                self.progress_updated.emit(f"正在处理第{row + 1}/{total_products}个商品")

                # 提取商品ID
                product_id = self.extract_product_id(link)
                if not product_id:
                    self.logger.error(f"无法提取商品ID: {link}")
                    continue

                # 查询商品标题（使用第一个日期）
                title = self.query_product_title(product_id, date_list[0])
                if title:
                    self.title_received.emit(row, title, link)
                else:
                    self.title_received.emit(row, "获取标题失败", link)

                # 使用线程池并发查询每天的成交量数据
                if self.speed_mode == "fast" and len(date_list) > 5:
                    # 快速模式：使用并发处理
                    with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_concurrent_requests) as executor:
                        # 提交所有日期的查询任务
                        futures = []
                        for date_index, date in enumerate(date_list):
                            if self.should_stop:
                                break
                            future = executor.submit(self.query_single_date_sales, product_id, date, row, date_index)
                            futures.append(future)

                        # 等待所有任务完成
                        for future in concurrent.futures.as_completed(futures):
                            if self.should_stop:
                                break
                            try:
                                future.result()  # 获取结果，如果有异常会抛出
                            except Exception as e:
                                self.logger.error(f"并发查询任务失败: {e}")
                else:
                    # 普通模式：串行处理
                    for date_index, date in enumerate(date_list):
                        if self.should_stop:
                            break

                        sales_count = self.query_sales_data(product_id, date)
                        self.sales_data_received.emit(row, date_index, sales_count)

                        # 添加延迟避免请求过快
                        time.sleep(self.request_delay)

                # 商品间添加延迟
                if not self.should_stop:
                    time.sleep(self.product_delay)

        except Exception as e:
            self.logger.error(f"普通模式查询过程出错: {e}")
            raise
    
    def query_batch_sales_data(self, product_ids: List[str], date: str) -> Dict[str, int]:
        """批量查询多个商品在指定日期的成交量数据"""
        try:
            # 注意：这里假设API支持批量查询，如果不支持则需要逐个查询
            # 当前快手API可能不支持真正的批量查询，所以这里仍然是逐个查询
            # 但通过并发处理来提高速度

            results = {}

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_concurrent_requests) as executor:
                # 创建查询任务
                future_to_id = {
                    executor.submit(self.query_sales_data, product_id, date): product_id
                    for product_id in product_ids
                }

                # 收集结果
                for future in concurrent.futures.as_completed(future_to_id):
                    if self.should_stop:
                        break

                    product_id = future_to_id[future]
                    try:
                        sales_count = future.result()
                        results[product_id] = sales_count
                    except Exception as e:
                        self.logger.error(f"批量查询商品{product_id}失败: {e}")
                        results[product_id] = 0

                    # 根据速度模式添加不同的延迟
                    if self.speed_mode == "ultra_fast":
                        time.sleep(0.001)  # 超快模式：1毫秒
                    elif self.speed_mode == "fast":
                        time.sleep(0.002)  # 快速模式：2毫秒
                    elif self.speed_mode == "normal":
                        time.sleep(0.005)  # 普通模式：5毫秒
                    else:
                        time.sleep(0.01)   # 慢速模式：10毫秒

            return results

        except Exception as e:
            self.logger.error(f"批量查询失败: {e}")
            return {product_id: 0 for product_id in product_ids}

    def run_batch_mode(self):
        """批量模式执行查询（最快速度）"""
        try:
            # 生成日期列表
            date_list = self.generate_date_list()
            total_products = len(self.product_links)

            # 提取所有商品ID
            product_data = []
            for row, link in enumerate(self.product_links):
                product_id = self.extract_product_id(link)
                if product_id:
                    product_data.append((row, link, product_id))

            if not product_data:
                self.error_occurred.emit("没有有效的商品链接")
                return

            # 先批量查询所有商品标题
            self.progress_updated.emit("正在批量查询商品标题...")
            for row, link, product_id in product_data:
                if self.should_stop:
                    break

                title = self.query_product_title(product_id, date_list[0])
                if title:
                    self.title_received.emit(row, title, link)
                else:
                    self.title_received.emit(row, "获取标题失败", link)

            # 按日期批量查询成交量数据
            product_ids = [item[2] for item in product_data]

            for date_index, date in enumerate(date_list):
                if self.should_stop:
                    break

                self.progress_updated.emit(f"正在查询日期 {date} ({date_index + 1}/{len(date_list)})")

                # 批量查询这一天所有商品的成交量
                sales_results = self.query_batch_sales_data(product_ids, date)

                # 发送结果
                for row, link, product_id in product_data:
                    if self.should_stop:
                        break

                    sales_count = sales_results.get(product_id, 0)
                    self.sales_data_received.emit(row, date_index, sales_count)

                # 日期间延迟 - 批量模式下可以更激进
                if self.speed_mode == "ultra_fast":
                    time.sleep(0.002)  # 超快模式：2毫秒
                elif self.speed_mode == "fast":
                    time.sleep(0.005)  # 快速模式：5毫秒
                elif self.speed_mode == "normal":
                    time.sleep(0.01)   # 普通模式：10毫秒
                else:
                    time.sleep(self.request_delay)  # 慢速模式：保持原设置

        except Exception as e:
            self.logger.error(f"批量查询过程出错: {e}")
            self.error_occurred.emit(f"批量查询过程出错: {str(e)}")

    def stop_query(self):
        """停止查询"""
        self.should_stop = True
        self.progress_updated.emit("正在停止查询...")
