2025-07-30 04:44:37,736 - INFO - DataCollector资源清理完成
2025-07-30 04:49:02,594 - INFO - DataCollector资源清理完成
2025-07-30 04:52:06,580 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '', '售卖渠道': '商品卡', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 04:52:06,595 - INFO - 类目数据加载成功
2025-07-30 04:52:06,599 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "categoryLevel2Id", "value": ["1662"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 04:52:06,603 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:07,549 - INFO - 成功获取 100 条基础数据
2025-07-30 04:52:07,552 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:07,555 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:07,555 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:08,537 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:08,560 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:08,560 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:09,532 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:09,537 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:09,551 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:10,557 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:10,564 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:10,569 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:11,572 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:11,593 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:11,630 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:12,600 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:12,610 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:12,643 - INFO - 成功加载 9 个Cookie
2025-07-30 04:52:12,956 - INFO - 正在关闭线程池...
2025-07-30 04:52:13,588 - INFO - DataCollector资源清理完成
2025-07-30 05:49:11,760 - INFO - DataCollector资源清理完成
2025-07-30 05:49:12,728 - INFO - DataCollector资源清理完成
2025-07-30 05:49:52,214 - INFO - DataCollector资源清理完成
2025-07-30 06:16:06,477 - INFO - DataCollector资源清理完成
2025-07-30 06:26:21,154 - INFO - DataCollector资源清理完成
2025-07-30 06:29:27,661 - INFO - DataCollector资源清理完成
2025-07-30 06:29:44,403 - INFO - DataCollector资源清理完成
2025-07-30 06:30:13,962 - INFO - DataCollector资源清理完成
2025-07-30 06:31:25,437 - INFO - DataCollector资源清理完成
2025-07-30 06:31:54,683 - INFO - DataCollector资源清理完成
2025-07-30 07:01:16,570 - INFO - DataCollector资源清理完成
2025-07-30 07:36:32,408 - INFO - DataCollector资源清理完成
2025-07-30 08:04:39,568 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '零食/坚果/特产', '二级类目': '饼干/膨化', '三级类目': '饼干', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 08:04:39,612 - INFO - 类目数据加载成功
2025-07-30 08:04:39,616 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["零食/坚果/特产"]}, {"code": "categoryLevel1Id", "value": ["8248"]}, {"code": "categoryLevel2Id", "value": ["21069"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 08:04:39,618 - INFO - 成功加载 9 个Cookie
2025-07-30 08:04:40,448 - WARNING - 响应中没有数据
2025-07-30 08:04:47,241 - INFO - DataCollector资源清理完成
2025-07-30 08:26:19,023 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '零食/坚果/特产', '二级类目': '饼干/膨化', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 08:26:19,061 - INFO - 类目数据加载成功
2025-07-30 08:26:19,065 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["零食/坚果/特产"]}, {"code": "categoryLevel1Id", "value": ["8248"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 08:26:19,071 - INFO - 成功加载 9 个Cookie
2025-07-30 08:26:19,914 - WARNING - 响应中没有数据
2025-07-30 08:26:31,407 - INFO - DataCollector资源清理完成
2025-07-30 08:26:56,655 - INFO - DataCollector资源清理完成
2025-07-30 15:13:56,317 - INFO - 设置筛选条件: {'日期': '2025-07-21-2025-07-27', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 15:13:56,335 - INFO - 类目数据加载成功
2025-07-30 15:13:56,338 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-21", "currentEndDay": "2025-07-27", "compareStartDay": "2025-07-14", "compareEndDay": "2025-07-20", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 15:13:56,339 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:57,614 - INFO - 成功获取 100 条基础数据
2025-07-30 15:13:57,618 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:57,618 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:57,619 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:58,772 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:58,788 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:58,827 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:59,860 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:59,910 - INFO - 成功加载 9 个Cookie
2025-07-30 15:13:59,973 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:00,989 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:01,093 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:01,108 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:02,134 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:02,141 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:02,280 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:03,178 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:03,216 - INFO - 成功加载 9 个Cookie
2025-07-30 15:14:03,222 - INFO - 正在关闭线程池...
2025-07-30 15:14:03,307 - INFO - DataCollector资源清理完成
2025-07-30 15:33:01,176 - INFO - 设置筛选条件: {'日期': '2025-07-21-2025-07-27', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 15:33:01,194 - INFO - 类目数据加载成功
2025-07-30 15:33:01,198 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-21", "currentEndDay": "2025-07-27", "compareStartDay": "2025-07-14", "compareEndDay": "2025-07-20", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 15:33:01,201 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:01,955 - INFO - 成功获取 100 条基础数据
2025-07-30 15:33:01,960 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:01,960 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:01,961 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:02,875 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:02,890 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:02,919 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:03,794 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:03,849 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:03,860 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:04,638 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:04,746 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:04,843 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:05,470 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:05,630 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:05,774 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:06,288 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:06,512 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:06,659 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:07,440 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:07,591 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:07,735 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:08,551 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:08,604 - INFO - 成功加载 9 个Cookie
2025-07-30 15:33:08,759 - INFO - 正在关闭线程池...
2025-07-30 15:33:08,887 - INFO - DataCollector资源清理完成
2025-07-30 16:08:35,580 - INFO - DataCollector资源清理完成
2025-07-30 16:08:45,540 - INFO - DataCollector资源清理完成
2025-07-30 16:24:52,738 - INFO - 设置筛选条件: {'日期': '2025-07-21-2025-07-27', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 16:24:52,758 - INFO - 类目数据加载成功
2025-07-30 16:24:52,761 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-21", "currentEndDay": "2025-07-27", "compareStartDay": "2025-07-14", "compareEndDay": "2025-07-20", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 16:24:52,763 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:53,929 - INFO - 成功获取 100 条基础数据
2025-07-30 16:24:53,932 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:53,934 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:53,937 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:54,994 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:54,994 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:55,000 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:56,071 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:56,083 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:56,136 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:57,134 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:57,261 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:57,292 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:58,195 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:58,377 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:58,393 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:59,458 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:59,483 - INFO - 成功加载 9 个Cookie
2025-07-30 16:24:59,549 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:00,525 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:00,750 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:00,763 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:01,499 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:01,794 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:01,824 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:02,364 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:02,812 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:02,836 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:03,329 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:03,960 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:03,960 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:04,236 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:05,001 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:05,079 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:05,219 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:06,079 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:06,244 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:06,289 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:07,167 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:07,267 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:07,496 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:08,160 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:08,309 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:08,443 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:09,149 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:09,300 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:09,504 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:10,133 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:10,338 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:10,467 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:11,151 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:11,391 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:11,566 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:12,078 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:12,335 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:12,564 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:13,000 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:13,324 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:13,691 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:13,926 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:14,282 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:14,753 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:15,104 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:15,291 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:15,791 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:16,131 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:16,232 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:16,849 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:17,124 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:17,143 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:17,838 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:18,145 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:18,162 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:18,795 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:19,117 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:19,126 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:19,822 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:20,214 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:20,237 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:20,845 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:21,254 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:21,276 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:21,858 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:22,429 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:22,436 - INFO - 成功加载 9 个Cookie
2025-07-30 16:25:22,513 - INFO - 正在关闭线程池...
2025-07-30 16:25:22,804 - INFO - DataCollector资源清理完成
2025-07-30 16:44:08,651 - INFO - DataCollector资源清理完成
2025-07-30 16:46:32,903 - INFO - DataCollector资源清理完成
2025-07-30 17:11:45,384 - INFO - DataCollector资源清理完成
2025-07-30 17:11:50,063 - INFO - DataCollector资源清理完成
2025-07-30 17:11:59,935 - INFO - DataCollector资源清理完成
2025-07-30 17:12:42,191 - INFO - DataCollector资源清理完成
2025-07-30 17:13:01,968 - INFO - DataCollector资源清理完成
2025-07-30 17:13:12,608 - INFO - DataCollector资源清理完成
2025-07-30 17:14:25,114 - INFO - DataCollector资源清理完成
2025-07-30 17:14:32,025 - INFO - DataCollector资源清理完成
2025-07-30 17:15:15,634 - INFO - DataCollector资源清理完成
2025-07-30 17:15:25,625 - INFO - DataCollector资源清理完成
2025-07-30 17:15:32,785 - INFO - DataCollector资源清理完成
2025-07-30 17:16:19,739 - INFO - DataCollector资源清理完成
2025-07-30 17:17:15,220 - INFO - DataCollector资源清理完成
2025-07-30 17:17:32,402 - INFO - DataCollector资源清理完成
2025-07-30 17:17:43,646 - INFO - DataCollector资源清理完成
2025-07-30 17:20:01,677 - INFO - DataCollector资源清理完成
2025-07-30 17:21:05,746 - INFO - DataCollector资源清理完成
2025-07-30 18:17:59,541 - INFO - DataCollector资源清理完成
2025-07-30 18:20:14,299 - INFO - DataCollector资源清理完成
2025-07-30 20:21:06,096 - INFO - DataCollector资源清理完成
2025-07-30 20:21:45,177 - INFO - DataCollector资源清理完成
2025-07-30 20:21:52,993 - INFO - DataCollector资源清理完成
2025-07-30 20:22:45,592 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '生鲜食品行业', '二级类目': '零食/坚果/特产', '三级类目': '糖果零食/果冻/布丁', '四级类目': '', '售卖渠道': '商品卡', '售卖形式': '自卖', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 20:22:45,607 - INFO - 类目数据加载成功
2025-07-30 20:22:45,612 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["生鲜食品行业"]}, {"code": "categoryLevel1Id", "value": ["1164"]}, {"code": "categoryLevel2Id", "value": ["8320"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["否"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 20:22:45,614 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:46,505 - INFO - 成功获取 100 条基础数据
2025-07-30 20:22:46,509 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:46,509 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:46,510 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:47,462 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:47,471 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:47,471 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:48,414 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:48,474 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:48,488 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:49,312 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:49,406 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:49,524 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:50,236 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:50,317 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:50,362 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:51,194 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:51,242 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:51,291 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:52,305 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:52,418 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:52,473 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:53,339 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:53,450 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:53,521 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:54,488 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:57,663 - INFO - 设置筛选条件: {'日期': '2025-07-21-2025-07-27', '一级类目': '生鲜食品行业', '二级类目': '零食/坚果/特产', '三级类目': '糖果零食/果冻/布丁', '四级类目': '', '售卖渠道': '商品卡', '售卖形式': '自卖', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 20:22:57,715 - INFO - 类目数据加载成功
2025-07-30 20:22:57,716 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-21", "currentEndDay": "2025-07-27", "compareStartDay": "2025-07-14", "compareEndDay": "2025-07-20", "param": [{"code": "industryId", "value": ["生鲜食品行业"]}, {"code": "categoryLevel1Id", "value": ["1164"]}, {"code": "categoryLevel2Id", "value": ["8320"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["否"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 20:22:57,718 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:58,897 - INFO - 成功获取 100 条基础数据
2025-07-30 20:22:58,913 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:58,913 - INFO - 成功加载 9 个Cookie
2025-07-30 20:22:58,914 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:00,211 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:00,211 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:00,211 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:01,349 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:01,371 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:01,371 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:02,490 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:02,535 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:02,535 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:03,466 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:03,547 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:03,597 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:04,494 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:04,563 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:04,649 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:11,394 - INFO - 设置筛选条件: {'日期': '2025-07-21-2025-07-27', '一级类目': '生鲜食品行业', '二级类目': '零食/坚果/特产', '三级类目': '肉干肉脯/熏腊卤味/肉类熟食', '四级类目': '', '售卖渠道': '商品卡', '售卖形式': '自卖', '品牌商品': '全部', '大牌大补': '全部'}
2025-07-30 20:23:11,409 - INFO - 类目数据加载成功
2025-07-30 20:23:11,411 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-21", "currentEndDay": "2025-07-27", "compareStartDay": "2025-07-14", "compareEndDay": "2025-07-20", "param": [{"code": "industryId", "value": ["生鲜食品行业"]}, {"code": "categoryLevel1Id", "value": ["1164"]}, {"code": "categoryLevel2Id", "value": ["8275"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["否"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-07-30 20:23:11,413 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:12,606 - INFO - 成功获取 100 条基础数据
2025-07-30 20:23:12,610 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:12,610 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:12,611 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:13,594 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:13,600 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:13,681 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:14,531 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:14,540 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:14,658 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:15,487 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:15,503 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:15,619 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:16,399 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:16,459 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:16,525 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:17,402 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:17,459 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:17,467 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:18,426 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:18,466 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:18,478 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:19,396 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:19,434 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:19,450 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:20,348 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:20,395 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:20,410 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:21,277 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:21,349 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:21,412 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:22,220 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:22,338 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:22,446 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:23,142 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:23,270 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:23,376 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:24,042 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:24,153 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:24,357 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:24,961 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:25,092 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:25,272 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:25,835 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:26,034 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:26,213 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:26,849 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:27,011 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:27,089 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:27,799 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:27,944 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:28,051 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:28,824 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:28,984 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:29,045 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:29,740 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:30,037 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:30,042 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:30,580 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:30,946 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:30,984 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:31,375 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:31,884 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:31,970 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:32,199 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:32,813 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:32,880 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:33,111 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:33,735 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:33,795 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:33,946 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:34,653 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:34,791 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:34,822 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:35,554 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:35,727 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:35,731 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:36,458 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:36,638 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:36,691 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:37,370 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:37,607 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:37,615 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:38,226 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:38,531 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:38,555 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:39,073 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:39,485 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:39,516 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:39,905 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:40,402 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:40,425 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:40,779 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:41,368 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:41,387 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:41,597 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:42,283 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:42,327 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:42,533 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:43,243 - INFO - 成功加载 9 个Cookie
2025-07-30 20:23:43,257 - INFO - 成功加载 9 个Cookie
2025-07-30 21:02:36,085 - INFO - DataCollector资源清理完成
2025-07-30 21:31:51,812 - INFO - DataCollector资源清理完成
2025-07-30 23:13:42,008 - INFO - DataCollector资源清理完成
2025-07-30 23:14:28,018 - INFO - DataCollector资源清理完成
2025-07-30 23:20:18,068 - INFO - DataCollector资源清理完成
