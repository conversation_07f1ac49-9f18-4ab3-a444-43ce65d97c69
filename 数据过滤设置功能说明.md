# 数据过滤设置功能说明

## 功能概述

在商品采集标签页中新增了"数据过滤设置"区域，提供两种数据过滤功能：

1. **成交指数区间过滤** - 控制采集范围，提高采集效率
2. **渠道占比区间过滤** - 控制数据显示，优化数据展示

## 界面布局

### 位置
- 数据过滤设置区域位于"条件筛选设置"区域的正下方
- 高度固定为100px，与条件筛选设置区域保持一致的样式

### 布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                    数据过滤设置                              │
├─────────────────────────┬───────────────────────────────────┤
│ 左侧50%：过滤设置        │ 右侧50%：功能按钮                  │
│                        │                                   │
│ 成交指数区间：           │ [登录] [开始采集] [停止采集]        │
│ [最小值] ~ [最大值]      │ [导出数据] [解析类目]              │
│                        │                                   │
│ 渠道占比区间：           │                                   │
│ [最小值%] ~ [最大值%]    │                                   │
└─────────────────────────┴───────────────────────────────────┘
```

## 功能详细说明

### 1. 成交指数区间过滤

#### 设置项
- **成交指数最小值**：默认值 1500
- **成交指数最大值**：默认值 999999

#### 运行逻辑
- 由于商品数据按成交指数从高到低排序
- 当采集到某个商品的成交指数小于设置的最小值时，**立即停止采集**后续商品
- 这样可以避免采集大量低成交指数的商品，提高采集效率

#### 使用示例
```
设置：最小值=2000，最大值=999999
结果：当遇到成交指数小于2000的商品时，停止采集该商品及其后面的所有商品
```

### 2. 渠道占比区间过滤

#### 设置项
- **渠道占比最小值**：默认值 85%
- **渠道占比最大值**：默认值 105%

#### 运行逻辑
- 仍然采集所有商品的数据（不影响采集过程）
- 在数据展示区域只显示渠道占比在设定范围内的商品
- 不符合条件的商品数据被过滤掉，不在界面上展示
- 但所有数据仍会保存在导出文件中

#### 使用示例
```
设置：最小值=85%，最大值=100%
结果：只有渠道占比在85%-100%之间的商品才会显示在数据表格中
```

## 数据持久化

### 保存位置
- 设置数据保存在 `data/Category data.txt` 文件中
- 与类目数据共享同一个文件，使用 `filter_settings` 字段存储

### 文件格式
```json
{
  "一级类目1": {
    "children": { ... }
  },
  "filter_settings": {
    "成交指数最小值": 1500,
    "成交指数最大值": 999999,
    "渠道占比最小值": 85,
    "渠道占比最大值": 105
  }
}
```

### 自动保存
- 当用户修改任何过滤设置输入框时，自动保存到文件
- 软件启动时自动从文件加载设置值
- 如果文件不存在或读取失败，使用默认数值

## 数据验证

### 输入验证
- **成交指数**：必须为正整数，最大值必须大于最小值
- **渠道占比**：必须为0-200之间的数值，最大值必须大于最小值

### 错误处理
- 输入无效数值时显示警告提示
- 验证失败时不允许开始采集
- 解析失败时使用默认值，不影响程序运行

## 使用流程

1. **设置过滤条件**
   - 在"数据过滤设置"区域输入期望的数值范围
   - 设置会自动保存，下次启动时自动加载

2. **开始采集**
   - 点击"开始采集"按钮
   - 系统会先验证过滤设置的有效性
   - 采集过程中应用成交指数过滤

3. **查看结果**
   - 表格中只显示符合渠道占比条件的商品
   - 状态栏会显示实际采集和显示的商品数量

4. **导出数据**
   - 导出的Excel文件包含所有采集到的数据
   - 包括被渠道占比过滤掉但仍被采集的商品

## 注意事项

1. **成交指数过滤**会影响采集效率，设置过高的最小值可能导致采集到的商品很少
2. **渠道占比过滤**只影响显示，不影响数据采集和导出
3. 设置修改后立即生效，无需重启程序
4. 建议根据实际业务需求合理设置过滤范围

## 技术实现

- 使用PyQt5/PyQt6的QLineEdit组件实现输入框
- 实时监听输入变化并自动保存设置
- 在数据采集器中集成过滤逻辑
- 支持多线程环境下的安全过滤操作
