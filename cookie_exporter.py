#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie导出工具 - Python版本
集成浏览器内核组件实现在程序中嵌入浏览器
"""

import sys
from urllib.parse import urlparse
from pathlib import Path

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                                QHBoxLayout, QWidget, QPushButton, QLabel,
                                QMessageBox, QProgressBar)
    from PyQt5.QtCore import QThread, pyqtSignal, QUrl
    from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEngineProfile
    from PyQt5.QtWebEngineCore import QWebEngineCookieStore
    PYQT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                                    QHBoxLayout, QWidget, Q<PERSON><PERSON><PERSON>utton, Q<PERSON><PERSON>l,
                                    Q<PERSON><PERSON>ageBox, QProgressBar)
        from PyQt6.QtCore import QThread, pyqtSignal, QUrl
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        from PyQt6.QtWebEngineCore import QWebEngineProfile, QWebEngineCookieStore
        PYQT_VERSION = 6
    except ImportError:
        print("错误：需要安装 PyQt5 或 PyQt6")
        sys.exit(1)


class CookieManager:
    """Cookie管理器"""

    def __init__(self, cookie_store):
        self.cookie_store = cookie_store
        self.cookies = []
        self.cookie_store.cookieAdded.connect(self.on_cookie_added)
        self.cookie_store.cookieRemoved.connect(self.on_cookie_removed)

    def on_cookie_added(self, cookie):
        """Cookie添加时的回调"""
        cookie_data = {
            'name': cookie.name().data().decode('utf-8'),
            'value': cookie.value().data().decode('utf-8'),
            'domain': cookie.domain(),
            'path': cookie.path(),
            'secure': cookie.isSecure(),
            'httpOnly': cookie.isHttpOnly()
        }

        # 避免重复添加
        existing_cookie = next((c for c in self.cookies
                              if c['name'] == cookie_data['name'] and
                                 c['domain'] == cookie_data['domain']), None)
        if existing_cookie:
            self.cookies.remove(existing_cookie)

        self.cookies.append(cookie_data)

    def on_cookie_removed(self, cookie):
        """Cookie移除时的回调"""
        cookie_name = cookie.name().data().decode('utf-8')
        cookie_domain = cookie.domain()
        self.cookies = [c for c in self.cookies
                       if not (c['name'] == cookie_name and c['domain'] == cookie_domain)]

    def get_cookies_for_domain(self, url):
        """获取指定域名的Cookie"""
        parsed_url = urlparse(url)
        hostname = parsed_url.hostname
        if not hostname:
            return []

        # 获取主域名（例如：xiaohongshu.com）
        domain_parts = hostname.split('.')
        if len(domain_parts) >= 2:
            main_domain = '.'.join(domain_parts[-2:])
        else:
            main_domain = hostname

        # 过滤相关域名的Cookie
        relevant_cookies = []
        for cookie in self.cookies:
            cookie_domain = cookie['domain']
            if (main_domain in cookie_domain or
                cookie_domain == '.' + main_domain or
                cookie_domain == main_domain or
                hostname in cookie_domain or
                cookie_domain.endswith('.' + main_domain)):
                relevant_cookies.append(cookie)

        return relevant_cookies


class CookieExportWorker(QThread):
    """Cookie导出工作线程"""
    finished = pyqtSignal(str, int)  # 结果消息, Cookie数量
    error = pyqtSignal(str)  # 错误消息
    progress = pyqtSignal(str)  # 进度消息
    url_changed = pyqtSignal(str)  # URL变化信号

    def __init__(self, cookie_manager, browser_page, browser_view):
        super().__init__()
        self.cookie_manager = cookie_manager
        self.browser_page = browser_page
        self.browser_view = browser_view
        self.js_cookies = []

        # 定义要采集的网页
        self.urls = [
            {
                'url': 'https://syt.kwaixiaodian.com/zones/home',
                'filename': 'cookies.txt',
                'name': '网页1'
            },
            {
                'url': 'https://cps.kwaixiaodian.com/pc/promoter/selection-center/home',
                'filename': 'cookies2.txt',
                'name': '网页2'
            }
        ]
        self.current_index = 0

    def run(self):
        try:
            self.progress.emit("开始采集Cookie...")
            self.process_next_url()

        except Exception as e:
            self.error.emit(f"导出 Cookie 时出错：{str(e)}")

    def process_next_url(self):
        """处理下一个URL"""
        if self.current_index < len(self.urls):
            url_info = self.urls[self.current_index]
            url = url_info['url']
            name = url_info['name']

            self.progress.emit(f"正在访问{name}: {url}")

            # 发送URL变化信号，让主线程加载页面
            self.url_changed.emit(url)

            # 等待页面加载
            self.msleep(3000)  # 等待3秒让页面完全加载

            # 获取并保存当前页面的Cookie
            self.export_current_cookies()

        else:
            # 所有URL处理完成
            self.finished.emit("所有网页Cookie采集完成！", 0)

    def export_current_cookies(self):
        """导出当前页面的Cookie"""
        try:
            url_info = self.urls[self.current_index]
            url = url_info['url']
            filename = url_info['filename']
            name = url_info['name']

            self.progress.emit(f"正在获取{name}的Cookie...")

            # 等待JavaScript Cookie获取完成
            self.msleep(1000)

            # 从Cookie管理器获取Cookie
            store_cookies = self.cookie_manager.get_cookies_for_domain(url)

            # 合并JavaScript获取的Cookie和存储的Cookie
            all_cookies = []

            # 添加JavaScript获取的Cookie
            for js_cookie in self.js_cookies:
                if js_cookie.get('name') and js_cookie.get('value'):
                    all_cookies.append(js_cookie)

            # 添加存储的Cookie（避免重复）
            for store_cookie in store_cookies:
                existing = next((c for c in all_cookies
                               if c.get('name') == store_cookie['name']), None)
                if not existing:
                    all_cookies.append(store_cookie)

            if not all_cookies:
                self.progress.emit(f"⚠️ {name}未找到Cookie，继续下一个...")
            else:
                # 格式化Cookie字符串
                cookie_string = '; '.join([f"{cookie['name']}={cookie['value']}"
                                         for cookie in all_cookies
                                         if cookie.get('name') and cookie.get('value')])

                if cookie_string:
                    # 创建data目录
                    data_dir = Path("data")
                    data_dir.mkdir(exist_ok=True)

                    # 保存Cookie到指定文件
                    cookie_file = data_dir / filename
                    with open(cookie_file, 'w', encoding='utf-8') as f:
                        f.write(cookie_string)

                    self.progress.emit(f"✅ {name}成功保存 {len(all_cookies)} 个Cookie到 {filename}")
                else:
                    self.progress.emit(f"⚠️ {name}未找到有效Cookie，继续下一个...")

            # 处理下一个URL
            self.current_index += 1
            self.msleep(1000)  # 等待1秒
            self.process_next_url()

        except Exception as e:
            self.error.emit(f"处理{name}时出错：{str(e)}")

    def set_js_cookies(self, cookies):
        """设置JavaScript获取的Cookie"""
        self.js_cookies = cookies if cookies else []


class CookieExporterMainWindow(QMainWindow):
    """Cookie导出工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_browser()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Cookies获取")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建工具栏布局
        toolbar_layout = QHBoxLayout()
        
        # 导出Cookie按钮
        self.export_button = QPushButton("一键导出 Cookie")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.export_button.clicked.connect(self.export_cookies)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
                font-weight: bold;
            }
        """)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        toolbar_layout.addWidget(self.status_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.export_button)
        
        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(self.progress_bar)
        
        # 创建浏览器视图
        self.browser = QWebEngineView()
        main_layout.addWidget(self.browser)
    
    def setup_browser(self):
        """设置浏览器"""
        # 创建自定义配置文件
        self.profile = QWebEngineProfile.defaultProfile()
        self.cookie_store = self.profile.cookieStore()

        # 创建Cookie管理器
        self.cookie_manager = CookieManager(self.cookie_store)

        # 加载默认页面
        self.browser.load(QUrl("https://syt.kwaixiaodian.com/zones/home"))

        # 连接URL变化信号
        self.browser.urlChanged.connect(self.on_url_changed)

    def on_url_changed(self, url):
        """URL变化时更新状态"""
        self.status_label.setText(f"当前页面: {url.toString()}")
    
    def export_cookies(self):
        """导出Cookie - 自动采集两个网页"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 创建工作线程
        self.worker = CookieExportWorker(self.cookie_manager, self.browser.page(), self.browser)
        self.worker.finished.connect(self.on_export_finished)
        self.worker.error.connect(self.on_export_error)
        self.worker.progress.connect(self.on_export_progress)
        self.worker.url_changed.connect(self.on_worker_url_changed)

        # 启动工作线程
        self.worker.start()

    def get_page_cookies(self):
        """获取当前页面的Cookie"""
        # 通过JavaScript获取Cookie
        script = """
        (function() {
            if (!document.cookie) return [];
            return document.cookie.split(';').map(cookie => {
                const parts = cookie.trim().split('=');
                const name = parts[0];
                const value = parts.slice(1).join('=');
                return {
                    name: name,
                    value: value || '',
                    domain: window.location.hostname
                };
            }).filter(cookie => cookie.name);
        })();
        """

        def handle_cookies(result):
            if result and self.worker:
                self.worker.set_js_cookies(result)

        self.browser.page().runJavaScript(script, handle_cookies)

    def on_worker_url_changed(self, url):
        """工作线程请求加载新URL"""
        self.browser.load(QUrl(url))
        # 等待页面加载完成后获取Cookie
        self.browser.loadFinished.connect(self.on_page_load_finished)

    def on_page_load_finished(self, success):
        """页面加载完成后获取Cookie"""
        if success and hasattr(self, 'worker') and self.worker:
            # 获取当前页面的Cookie
            self.get_page_cookies()
            # 断开信号避免重复连接
            try:
                self.browser.loadFinished.disconnect(self.on_page_load_finished)
            except:
                pass

    def on_export_progress(self, message):
        """更新导出进度"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
                font-weight: bold;
            }
        """)
    
    def on_export_finished(self, message, cookie_count):
        """导出完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
                font-weight: bold;
            }
        """)

        # 显示完成消息
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle("采集完成")
        msg_box.setText("Cookie采集完成！\n\n"
                       "网页1 (快手小店后台): data/cookies.txt\n"
                       "网页2 (快手小店CPS): data/cookies2.txt\n\n"
                       "请查看data目录下的文件。\n\n"
                       "浏览器窗口将在0.5秒后自动关闭。")
        msg_box.setStandardButtons(QMessageBox.Ok)

        # 显示消息框
        msg_box.exec_() if PYQT_VERSION == 5 else msg_box.exec()

        # 延迟0.05秒后自动关闭窗口
        if PYQT_VERSION == 5:
            from PyQt5.QtCore import QTimer
        else:
            from PyQt6.QtCore import QTimer
        QTimer.singleShot(5, self.close_application)
    
    def on_export_error(self, error_message):
        """导出错误"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(error_message)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                font-weight: bold;
            }
        """)
        
        QMessageBox.critical(self, "错误", error_message)

    def close_application(self):
        """关闭应用程序"""
        self.close()
        # 确保应用程序完全退出
        import sys
        sys.exit(0)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("Cookie导出工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = CookieExporterMainWindow()
    window.show()
    
    sys.exit(app.exec_() if PYQT_VERSION == 5 else app.exec())

if __name__ == "__main__":
    main()