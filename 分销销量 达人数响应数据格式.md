# A接口
{
    "result": 1,
    "data": [
        {
            "activityProfitAmount": "",
            "logisticsId": 0,
            "itemDisplayStatus": 0,
            "activityBeginTime": 0,
            "itemTagDto": [
                {
                    "tagCode": "distribute_send_ensure",
                    "tagDesc": "发货保障",
                    "tagValue": "1",
                    "fireFlag": false
                },
                {
                    "tagCode": "distribute_return_back_freight",
                    "tagDesc": "退货补运费",
                    "tagValue": "1",
                    "fireFlag": false
                },
                {
                    "tagCode": "distribute_fast_refund",
                    "tagDesc": "极速退款",
                    "tagValue": "1",
                    "fireFlag": false
                },
                {
                    "tagCode": "distribute_fake_one_pay_n",
                    "tagDesc": "假一赔十",
                    "tagValue": "1",
                    "fireFlag": false
                }
            ],
            "distributeItemId": 884641058189,
            "ska": 0,
            "bestCommissionType": 6,
            "freeShipment": 0,
            "sampleStatus": 0,
            "activityId": 0,
            "distributeType": 0,
            "showPopupStatusDesc": "",
            "relItemId": 24635699838189,
            "couponAmount": "",
            "sellerId": 3627075189,
            "bestCommissionId": 1907192746189,
            "activityStatus": 0,
            "shareDisabled": 0,
            "goodRateCnt7d": 0,
            "webLogParam": "{\"kwaiselectMode\":0,\"distrLiveDistorType\":\"other\",\"promoterWorthType\":\"UNKNOWN\",\"requestId\":\"436c6caf-4259-45bf-aad1-b2a46f2ecf58\"}",
            "ext": {
                "bizCode": "PC_SELECTION_CENTER",
                "scene": "recoKeywordSearchForPC",
                "itemSellerName": "天昊家居个体店",
                "dailyCommission": "0",
                "activityOrderNum": "0",
                "isHotSale": "0",
                "serverExpTag": "1_u/52792861439896_mr999999",
                "promoterHeadIconList": "[]",
                "investmentActivityId": "0",
                "investmentActivityStatus": "0",
                "matchScene": "accurateSearch",
                "decisionPageUrl": "https://app.kwaixiaodian.com/page/kwaishop-cps-selection-decision-app?layoutType=4&entranceScene=accurateSearch&relItemId=24635699838189",
                "shopLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-shop-goods-app?layoutType=4&shopId=3627075189&shopName=",
                "shopText": ""
            },
            "freeSample": false,
            "hasDistributePlan": false,
            "itemTag": [],
            "itemDisplayReason": "",
            "titleTagDto": [
                {
                    "tagCode": "trust_buy",
                    "tagDesc": "信任购",
                    "tagImgUrl": "https://w2.eckwai.com/kos/nlav12333/cps-assets/trust-purchase.1c259645322c7193.png"
                }
            ],
            "crossBoarder": false,
            "promoterCount": 8599,
            "chosenItemTag": "",
            "relVideo": {
                "nickname": "",
                "imgUrl": "",
                "videoUrl": "",
                "viewCount": 0,
                "headImg": "",
                "fansNum": 0,
                "kwaiUrl": "",
                "displayTime": "",
                "caption": ""
            },
            "activityEndTime": 0,
            "sourceType": 0,
            "soldCountThirtyDays": 16280,
            "brandId": 0,
            "itemLinkUrl": "",
            "sAGoods": false,
            "fsTeam": 0,
            "reservePrice": "16.90",
            "commissionRate": "10",
            "sellPoint": [],
            "rankInfo": {
                "rankText": "入选二折伞爆款榜",
                "rankNum": "TOP3",
                "rankLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-galaxy-app/landing/category?layoutType=4&configurationId=6&tag=distribute_leaf_category_rank_22486&trackRankType=2",
                "tagId": 0,
                "tagName": "",
                "categoryId": 0,
                "categoryName": ""
            },
            "itemTitle": "【24骨】双骨龙全自动一键开收晴雨两用黑胶涂层太阳伞防紫外线",
            "profitAmount": "1.69",
            "recoReason": [
                {
                    "name": "近x天佣金率稳定",
                    "desc": "近15天佣金率稳定"
                }
            ],
            "investmentActivityStatus": 0,
            "itemTagAttr": {},
            "itemChannel": 0,
            "sellerName": "天昊家居个体店",
            "exposureWeightType": 0,
            "tagText": "",
            "zkFinalPrice": "16.90",
            "coverMd5": "",
            "relLive": {
                "liveImg": "",
                "liveLink": "",
                "promoterId": 0,
                "promoterLogo": "",
                "promoterName": ""
            },
            "serverExpTag": "1_u/52792861439896_mr999999",
            "secondActivityId": 0,
            "titleHeadIcon": [],
            "stepCommissionInfo": {
                "stepNum": 0,
                "beforeStepProfitAmount": "",
                "beforeStepCommissionRate": "",
                "afterStepProfitAmount": "",
                "afterStepCommissionRate": "",
                "startTime": 0,
                "endTime": 0
            },
            "itemImgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-3627075189-82f05bc1ff1d49d79af0f2034f411a1c.jpg",
            "channelId": 0,
            "shelfItemStatus": 0,
            "couponRemainCount": 0,
            "waistCoverShowInfo": {
                "waistName": "",
                "waistImgUrl": "",
                "discountPrice": "",
                "discountDefaultText": "",
                "discountBgColor": "",
                "themeUrl": "",
                "themeSceneId": 0,
                "tagId": 0
            },
            "hasRecoReason": true,
            "activityCommissionRate": "",
            "isAdd": 0,
            "soldCountYesterday": 218,
            "investmentActivityId": 0,
            "showPopupStatusType": 0,
            "isStepCommission": false,
            "itemData": [],
            "isHealthCategory": false,
            "linkType": 0,
            "activityType": 0,
            "categoryId": 0
        }
    ],
    "requestId": "753720785706769970",
    "pcursor": "20",
    "errorMsg": "SUCCESS"
}


# B接口
{
    "result": 1,
    "error_msg": "SUCCESS",
    "data": {
        "component": [
            {
                "name": "mtp_headImage",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 633
            },
            {
                "name": "mtp_card",
                "renderType": "RN",
                "type": "viewNormal",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 655
            },
            {
                "name": "mtp_goodsTitle",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 3,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 634
            },
            {
                "name": "mtp_priceInfo",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 636
            },
            {
                "name": "mtp_commission",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 637
            },
            {
                "name": "mtp_stepCommission",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 643
            },
            {
                "name": "mtp_saleInfo",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 638
            },
            {
                "name": "mtp_hotRankInfo",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 640
            },
            {
                "name": "mtp_trustIcon",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 641
            },
            {
                "name": "mtp_express",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 642
            },
            {
                "name": "mtp_buyReturn",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 644
            },
            {
                "name": "mtp_goodsEvaluate",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 654
            },
            {
                "name": "mtp_storeEntrance",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 651
            },
            {
                "name": "mtp_merchantsPromotion",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 653
            },
            {
                "name": "mtp_videoList",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 647
            },
            {
                "name": "mtp_topSaleCardList",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 648
            },
            {
                "name": "mtp_popularizeChart",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 649
            },
            {
                "name": "mtp_decisionInfo_bottom",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 646
            },
            {
                "name": "infinity_root_container",
                "renderType": "RN",
                "type": "viewNormal",
                "ver": 4,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 528
            },
            {
                "name": "mtp_tagList",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 2,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 639
            },
            {
                "name": "mtp_recommendedTags",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 686
            },
            {
                "name": "mtp_sellerPoint",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 687
            },
            {
                "name": "scroll_node",
                "renderType": "RN",
                "type": "viewNormal",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 713
            },
            {
                "name": "SelectionNavBar",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 2,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 525
            },
            {
                "name": "ActivityPriceCard",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 969
            },
            {
                "name": "mtp_detail_info_container",
                "renderType": "RN",
                "type": "viewNormal",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 1273
            },
            {
                "name": "mtp_decision_material_guide",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 1989
            },
            {
                "name": "exemptReview",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 2776
            },
            {
                "name": "infinity_root_node",
                "renderType": "NATIVE",
                "type": "viewNormal",
                "ver": 1,
                "cid": 0
            }
        ],
        "data": {
            "mtp_goodsEvaluate_GYeZHNsw01o26OSU": {
                "instanceId": "mtp_goodsEvaluate_GYeZHNsw01o26OSU",
                "componentInstanceKey": 688245001,
                "name": "mtp_goodsEvaluate",
                "cid": 654,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "totalCount": 10000,
                    "normGoodCommentRate": 98,
                    "tagStatistics": [
                        {
                            "tagId": 10,
                            "tagName": "有图/视频",
                            "count": 2920
                        },
                        {
                            "tagId": 2100052,
                            "tagName": "回购意愿强",
                            "count": 3132
                        },
                        {
                            "tagId": 2100082,
                            "tagName": "质量好",
                            "count": 2899
                        },
                        {
                            "tagId": 2100012,
                            "tagName": "性价比高",
                            "count": 2698
                        },
                        {
                            "tagId": 2100072,
                            "tagName": "很满意",
                            "count": 2133
                        },
                        {
                            "tagId": 2100022,
                            "tagName": "发货速度快",
                            "count": 2113
                        },
                        {
                            "tagId": 2100042,
                            "tagName": "描述一致",
                            "count": 201
                        },
                        {
                            "tagId": 2726902,
                            "tagName": "包装精致",
                            "count": 124
                        }
                    ],
                    "comments": [
                        {
                            "commentId": 28029497473,
                            "commentUserAvatar": "https://p4-ec.ecukwai.com/uhead/AB/2017/12/29/08/BMjAxNzEyMjkwODE1MDNfNzg1NTU3MzEzXzJfaGQ3Ml85MTI=_s.jpg",
                            "commentUserName": "风**",
                            "content": "挺不错！味道好，份量很足。",
                            "commentImgUrls": [
                                "https://p2-ec.ecukwai.com/bs2/image-item-comment-ugc/aW1hZ2UtaXRlbS1jb21tZW50LXVnYzpJVEVNX0NPTU1FTlRfVUdDOjc4NTU1NzMxMzpNRVJDSEFOVDpbQkA3YmZmYmVlNzo4MDIzMjEzNTgyMzEz.jpg"
                            ]
                        },
                        {
                            "commentId": 29479295359,
                            "commentUserAvatar": "https://p4-ec.ecukwai.com/uhead/AB/2025/06/30/23/BMjAyNTA2MzAyMzI1NDVfMTQ4MDQ5MzUyNF8yX2hkOTE0XzMwOQ==_s.jpg",
                            "commentUserName": "玉**",
                            "content": "糖果已收到，质量很好，口味独特，非常喜欢，下次还会回购的",
                            "commentImgUrls": [
                                "https://p4-ec.ecukwai.com/bs2/image-item-comment-ugc/aW1hZ2UtaXRlbS1jb21tZW50LXVnYzpJVEVNX0NPTU1FTlRfVUdDOjE0ODA0OTM1MjQ6TUVSQ0hBTlQ6W0JANmEyMzljYTQ6MTAxMzY4ODk4Nzc1MjQ%3D.jpg",
                                "https://p4-ec.ecukwai.com/bs2/image-item-comment-ugc/aW1hZ2UtaXRlbS1jb21tZW50LXVnYzpJVEVNX0NPTU1FTlRfVUdDOjE0ODA0OTM1MjQ6TUVSQ0hBTlQ6W0JANWFlYTJiYzY6MTAxMzg4NzUxNzQ1MjQ%3D.jpg",
                                "https://p2-ec.ecukwai.com/bs2/image-item-comment-ugc/aW1hZ2UtaXRlbS1jb21tZW50LXVnYzpJVEVNX0NPTU1FTlRfVUdDOjE0ODA0OTM1MjQ6TUVSQ0hBTlQ6W0JAM2FlYjU3NDc6MTAxMzk4MjM0NzY1MjQ%3D.jpg"
                            ]
                        }
                    ],
                    "sellerId": 4153925705,
                    "relItemId": 23095789833705
                },
                "track": {}
            },
            "mtp_card_CgEg27LO0OW2ypP7": {
                "instanceId": "mtp_card_CgEg27LO0OW2ypP7",
                "componentInstanceKey": *********,
                "name": "mtp_card",
                "cid": 655,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "mtp_topSaleCardList_6WIwyvODIfJxhW0g": {
                "instanceId": "mtp_topSaleCardList_6WIwyvODIfJxhW0g",
                "componentInstanceKey": 679245004,
                "name": "mtp_topSaleCardList",
                "cid": 648,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "distributeItemId": 776558067705,
                    "rankInfo": [
                        {
                            "authorHeadImg": "https://p4-ec.ecukwai.com/uhead/AB/2024/07/03/08/BMjAyNDA3MDMwODU0NDZfMzM4ODQ3ODA5OF8xX2hkODAwXzc3NA==_s.jpg",
                            "authorName": "七七好物优选",
                            "rank": 1,
                            "rankDiff": 0,
                            "authorId": 3388478098,
                            "playUrl": "kwai://merchantinterpretation?itemId=HtWYG_qb8wg&sellerId=yJyM9Mj1Stw&photoUrls=%5B%7B%22cdn%22%3A%22v4-ec.ecukwai.com%22%2C%22url%22%3A%22http%3A%2F%2Fv4-ec.ecukwai.com%2Fmediacloud%2Fmerchant_record%2Frecord_video%2F2da5e8cbf075ba2e-caf27fa20e7c4694d7000e9ec700ae26-mp4_remux.mp4%22%7D%5D&coverUrls=%5B%7B%22cdn%22%3A%22v4-ec.ecukwai.com%22%2C%22url%22%3A%22http%3A%2F%2Fv4-ec.ecukwai.com%2Fmediacloud%2Fmerchant_record%2Frecord_video%2F2da5e8cbf075ba2e-e69851da1dd945de68326845ff4d1ac7-first_frame.jpg%22%7D%5D&entranceType=4&requestType=1&serverExpTag="
                        },
                        {
                            "authorHeadImg": "https://p4-ec.ecukwai.com/uhead/AB/2024/05/16/07/BMjAyNDA1MTYwNzI4NDFfMjQ0NzU1MDQ2N18xX2hkMTQ1XzUwNQ==_s.jpg",
                            "authorName": "木木在努力",
                            "rank": 2,
                            "rankDiff": 0,
                            "authorId": 2447550467,
                            "playUrl": ""
                        },
                        {
                            "authorHeadImg": "https://p4-ec.ecukwai.com/uhead/AB/2024/12/02/16/BMjAyNDEyMDIxNjA5NDlfMTM2NTA2ODQ5N18yX2hkNTcyXzMyNw==_s.jpg",
                            "authorName": "瑜大商品号（授权）",
                            "rank": 3,
                            "rankDiff": 0,
                            "authorId": 1365068497,
                            "playUrl": ""
                        }
                    ]
                },
                "track": {}
            },
            "mtp_decision_material_guide_yHZFiUq8HKxhAgyD": {
                "instanceId": "mtp_decision_material_guide_yHZFiUq8HKxhAgyD",
                "componentInstanceKey": 1670245002,
                "name": "mtp_decision_material_guide",
                "cid": 1989,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "materialGuideNum": 17
                },
                "track": {}
            },
            "mtp_storeEntrance_wl8b7ckVR4C9To2A": {
                "instanceId": "mtp_storeEntrance_wl8b7ckVR4C9To2A",
                "componentInstanceKey": 679245003,
                "name": "mtp_storeEntrance",
                "cid": 651,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "shopInfo": {
                        "shopId": 4153925705,
                        "shopName": "伈圣食品专营店",
                        "shopIconUrl": "https://p4-ec.ecukwai.com/uhead/AB/2024/05/28/15/BMjAyNDA1MjgxNTUxMzdfNDE1MzkyNTcwNV8xX2hkNzgxXzk1_s.jpg",
                        "distributeSoldAmount": "63314",
                        "soldAmountTabText": "近半年分销销量",
                        "storeScoreInfo": {
                            "score": "4.79",
                            "level": "优秀",
                            "rank": 73,
                            "showName": "店铺体验分",
                            "fallbackText": "",
                            "storeScoreQuadrantList": [
                                {
                                    "score": "4.83",
                                    "name": "商品体验",
                                    "level": "优秀"
                                },
                                {
                                    "score": "4.86",
                                    "name": "物流体验",
                                    "level": "优秀"
                                },
                                {
                                    "score": "4.63",
                                    "name": "售后服务",
                                    "level": "良好"
                                }
                            ]
                        },
                        "shopLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-shop-goods-app?layoutType=4&shopId=4153925705&shopName=伈圣食品专营店"
                    },
                    "distributeType": 6
                },
                "track": {}
            },
            "mtp_trustIcon_HpSlZgWMpY9Q6ycj": {
                "instanceId": "mtp_trustIcon_HpSlZgWMpY9Q6ycj",
                "componentInstanceKey": 676245002,
                "name": "mtp_trustIcon",
                "cid": 641,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            12,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "relItemId": 23095789833705
                },
                "track": {}
            },
            "mtp_sellerPoint_DgBMHwnGrd5w59c1": {
                "instanceId": "mtp_sellerPoint_DgBMHwnGrd5w59c1",
                "componentInstanceKey": 696245002,
                "name": "mtp_sellerPoint",
                "cid": 687,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "sellerPointContents": []
                },
                "track": {}
            },
            "KUAISHOU_ERA_ROOT": {
                "instanceId": "KUAISHOU_ERA_ROOT",
                "componentInstanceKey": 1001,
                "name": "infinity_root_node",
                "cid": 0,
                "ver": 1,
                "style": {},
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "exemptReview_IxGUtDDDoMoENxqB": {
                "instanceId": "exemptReview_IxGUtDDDoMoENxqB",
                "componentInstanceKey": 1907245004,
                "name": "exemptReview",
                "cid": 2776,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "sampleCheckInfo": []
                },
                "track": {}
            },
            "mtp_stepCommission_F4KZDOJuVt1VcSPQ": {
                "instanceId": "mtp_stepCommission_F4KZDOJuVt1VcSPQ",
                "componentInstanceKey": 686245002,
                "name": "mtp_stepCommission",
                "cid": 643,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            12,
                            12,
                            12
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "stepCommission": false
                },
                "track": {}
            },
            "mtp_goodsTitle_OOi3gcD1ntWEHjWZ": {
                "instanceId": "mtp_goodsTitle_OOi3gcD1ntWEHjWZ",
                "componentInstanceKey": 686245001,
                "name": "mtp_goodsTitle",
                "cid": 634,
                "ver": 3,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            12,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "itemTitle": "12.9元抢500g/袋俄罗斯风味混合糖巧克力多种口味夹心软牛扎糖果",
                    "titleTagDto": [],
                    "sourceType": 99,
                    "itemLinkUrl": "https://app.kwaixiaodian.com/merchant/shop/detail?id=23095789833705&layoutType=4",
                    "isAdd": 0
                },
                "track": {}
            },
            "mtp_priceInfo_A2Kt9FqkvJOPz3Ye": {
                "instanceId": "mtp_priceInfo_A2Kt9FqkvJOPz3Ye",
                "componentInstanceKey": 681245004,
                "name": "mtp_priceInfo",
                "cid": 636,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            12,
                            12,
                            12
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "zkFinalPrice": 1290,
                    "reservePrice": 1290,
                    "couponAmount": 0,
                    "crossBoarder": false
                },
                "track": {}
            },
            "mtp_tagList_mHvs4GHsmv1CVQak": {
                "instanceId": "mtp_tagList_mHvs4GHsmv1CVQak",
                "componentInstanceKey": 679245012,
                "name": "mtp_tagList",
                "cid": 639,
                "ver": 2,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "mtp_recommendedTags_UDc5OUuKBryJq4SH": {
                "instanceId": "mtp_recommendedTags_UDc5OUuKBryJq4SH",
                "componentInstanceKey": 701245002,
                "name": "mtp_recommendedTags",
                "cid": 686,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            6,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "hasRecoReason": true,
                    "recoReasonInfoList": [
                        {
                            "name": "N天降价",
                            "desc": "30天降8元"
                        },
                        {
                            "name": "近x天佣金率稳定",
                            "desc": "近15天佣金率稳定"
                        },
                        {
                            "name": "N人评价",
                            "desc": "2899人评价“质量好”"
                        }
                    ]
                },
                "track": {}
            },
            "mtp_express_H9CWlNDqrZSdp4UB": {
                "instanceId": "mtp_express_H9CWlNDqrZSdp4UB",
                "componentInstanceKey": 687245002,
                "name": "mtp_express",
                "cid": 642,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "express": {
                        "deliveryStatus": 1,
                        "deliveryText": "48小时内从山东省临沂市发货",
                        "postageStatus": 1,
                        "postageText": "部分地区包邮",
                        "postType": 5,
                        "noPostageArea": "宁夏回族自治区,新疆维吾尔自治区,西藏自治区,海南省,甘肃省,内蒙古自治区,青海省",
                        "postageTextDesc": "售卖部分地区包邮商品时，请提醒消费者关注邮费信息"
                    }
                },
                "track": {}
            },
            "mtp_headImage_sVBoVmgR43OV5Cb1": {
                "instanceId": "mtp_headImage_sVBoVmgR43OV5Cb1",
                "componentInstanceKey": 675245004,
                "name": "mtp_headImage",
                "cid": 633,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "itemImgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-9c920b9b342c48bb8fbc2a1ed2626567.jpg",
                    "itemImgUrls": [
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-9c920b9b342c48bb8fbc2a1ed2626567.jpg",
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-719a0470b6e44c948c2c1a82dc643070.jpg",
                        "https://p2-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-8be5fe76d36d41a9badc33d193652ae8.jpg",
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-1b8c6b95617a4196a1390942956e8087.jpg",
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-293b0fc9686c4edc81c0581a1f86168d.jpg",
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-2264a5ff9c4b454bb4639d67bb1930cf.jpg",
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-24e6e72ea8474c20875c6f063ac03aa8.jpg",
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-aab14b6abeba4a9bb879ca6d96c9e0b2.jpg",
                        "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-a8f7a938bfa244aa89993a9b79a87cd2.jpg"
                    ],
                    "handCard": {
                        "distributeItemId": 0,
                        "relItemId": 0,
                        "itemTitle": "",
                        "itemImgUrl": "",
                        "reservePrice": "",
                        "zkFinalPrice": "",
                        "skuInfo": [],
                        "deliveryTime": "",
                        "postageText": "",
                        "deliveryPlace": "",
                        "notDeliveryPlace": [],
                        "sellingPoints": [],
                        "status": 0
                    },
                    "display": false
                },
                "track": {}
            },
            "mtp_detail_info_container_ilBeuZBTjMeYrSNb": {
                "instanceId": "mtp_detail_info_container_ilBeuZBTjMeYrSNb",
                "componentInstanceKey": 1097245003,
                "name": "mtp_detail_info_container",
                "cid": 1273,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            12,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "mtp_merchantsPromotion_xwUb6DrWfomKeMDK": {
                "instanceId": "mtp_merchantsPromotion_xwUb6DrWfomKeMDK",
                "componentInstanceKey": 684245002,
                "name": "mtp_merchantsPromotion",
                "cid": 653,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "sellerId": 4153925705,
                    "shopName": "伈圣食品专营店",
                    "topItemList": [
                        {
                            "distributeItemId": 758078307705,
                            "relItemId": 22995474312705,
                            "itemImgUrl": "https://p2-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-082e277fd95a4fc8b5d59c427b994c1a.jpg",
                            "itemPrice": "9.90",
                            "itemTitle": "{9.9元抢1斤}爆浆太妃糖巧克力流心结婚喜糖生日寿宴好吃美味零食",
                            "profitAmount": "1.98",
                            "totalSaleAmount": 161,
                            "totalSaleAmountDesc": {
                                "unit": "",
                                "value": "161"
                            },
                            "bestCommissionId": 1478727124705,
                            "bestCommissionType": 1
                        },
                        {
                            "distributeItemId": 891914046705,
                            "relItemId": 24797428033705,
                            "itemImgUrl": "https://p2-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-9c920b9b342c48bb8fbc2a1ed2626567.jpg",
                            "itemPrice": "12.90",
                            "itemTitle": "【喜糖混合装】12.9元抢500g/袋俄罗斯风味混合糖巧克力多种口味",
                            "profitAmount": "2.06",
                            "totalSaleAmount": 148,
                            "totalSaleAmountDesc": {
                                "unit": "",
                                "value": "148"
                            },
                            "bestCommissionId": 1957007627705,
                            "bestCommissionType": 1
                        },
                        {
                            "distributeItemId": 777943620705,
                            "relItemId": 23165697325705,
                            "itemImgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-4153925705-ff8d392f4d7a45bfa5934387db4dd484.jpg",
                            "itemPrice": "12.90",
                            "itemTitle": "【12.9元抢500g/袋】俄罗斯风味夹心混合糖巧克力多种口味年货糖",
                            "profitAmount": "2.06",
                            "totalSaleAmount": 124,
                            "totalSaleAmountDesc": {
                                "unit": "",
                                "value": "124"
                            },
                            "bestCommissionId": 1527028730705,
                            "bestCommissionType": 6
                        }
                    ],
                    "total": 3,
                    "hotSaleLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-shop-goods-app?layoutType=4&shopId=4153925705&shopName=伈圣食品专营店&initOrderType=6"
                },
                "track": {}
            },
            "ActivityPriceCard_pOuFpShaJPfTXoC0": {
                "instanceId": "ActivityPriceCard_pOuFpShaJPfTXoC0",
                "componentInstanceKey": 914245004,
                "name": "ActivityPriceCard",
                "cid": 969,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            10,
                            10,
                            0,
                            10
                        ],
                        "margin": [
                            10,
                            0,
                            10,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "mtp_decisionInfo_bottom_qkcyahWVmM0XV3yZ": {
                "instanceId": "mtp_decisionInfo_bottom_qkcyahWVmM0XV3yZ",
                "componentInstanceKey": 678245004,
                "name": "mtp_decisionInfo_bottom",
                "cid": 646,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "shareDisabled": 3,
                    "stepCommission": false,
                    "bestCommissionId": 1517207979705,
                    "bestCommissionType": 6,
                    "commissionRate": 16,
                    "commissionRateStr": "16",
                    "relItemId": 23095789833705,
                    "sourceType": 99,
                    "itemLinkUrl": "https://app.kwaixiaodian.com/merchant/shop/detail?id=23095789833705&layoutType=4",
                    "distributeType": 6,
                    "isAdd": 0,
                    "sampleApplyType": 1,
                    "isExempted": false,
                    "exemptedType": 0,
                    "ongoingApplyId": 0,
                    "applyRestrict": "",
                    "signStatus": 2,
                    "successOrderCount": 0,
                    "liveVisitorCount": 0,
                    "videoViewCount": 0,
                    "zkFinalPrice": 1290,
                    "couponAmount": 0,
                    "contactInfo": {
                        "contactUserType": 1,
                        "contactList": [
                            {
                                "contactId": 121475,
                                "contactName": "龙先生",
                                "contactType": 1,
                                "contactStatus": 1,
                                "contactDetail": "16631126305",
                                "createTime": 1704872270699,
                                "updateTime": 1742731800814,
                                "weChat": "Lady-Liberty888",
                                "imContactLink": ""
                            }
                        ]
                    },
                    "recoReasonInfoList": [
                        {
                            "name": "N天降价",
                            "desc": "30天降8元"
                        },
                        {
                            "name": "近x天佣金率稳定",
                            "desc": "近15天佣金率稳定"
                        },
                        {
                            "name": "N人评价",
                            "desc": "2899人评价“质量好”"
                        }
                    ],
                    "hasRecoReason": true,
                    "investmentActivityId": 0,
                    "webLogParam": {
                        "kwaiselectMode": 0
                    },
                    "ext": {
                        "matchScene": ""
                    }
                },
                "track": {}
            },
            "mtp_hotRankInfo_h3fROxMTqvkkiCPW": {
                "instanceId": "mtp_hotRankInfo_h3fROxMTqvkkiCPW",
                "componentInstanceKey": 685245002,
                "name": "mtp_hotRankInfo",
                "cid": 640,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            12,
                            0,
                            12
                        ],
                        "margin": [
                            12,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "rankInfo": {}
                },
                "track": {}
            },
            "mtp_buyReturn_dtzrC2FZqDnLGYw1": {
                "instanceId": "mtp_buyReturn_dtzrC2FZqDnLGYw1",
                "componentInstanceKey": 682245003,
                "name": "mtp_buyReturn",
                "cid": 644,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "title": "",
                    "sampleRule": {}
                },
                "track": {}
            },
            "mtp_commission_BJK17v4ZBSVYkWul": {
                "instanceId": "mtp_commission_BJK17v4ZBSVYkWul",
                "componentInstanceKey": 683245002,
                "name": "mtp_commission",
                "cid": 637,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            12,
                            0,
                            12
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "commission": {
                        "commissionRate": 16,
                        "profitAmount": "2.06",
                        "commissionRateStr": "16",
                        "bestCommissionId": 1517207979705,
                        "bestCommissionType": 6
                    },
                    "crossBoarder": false,
                    "leaderInfo": {
                        "hasData": true,
                        "leaderId": 30532053,
                        "leaderName": "……#星空团长精选",
                        "leaderHeadIcon": "http://p2.a.yximgs.com/uhead/AB/2024/11/08/10/BMjAyNDExMDgxMDUxNTBfMzA1MzIwNTNfMl9oZDQyNl81ODM=_s.jpg",
                        "leaderIntro": "聊高佣·帮申样·高响应",
                        "jumpUrl": "https://app.kwaixiaodian.com/page/kwaishop-cps-hybrid/recommend/leader/activity_goods?layoutType=3&promoterId=30532053",
                        "leaderInterestList": [
                            "https://p4-ec.ecukwai.com/udata/pkg/KS-Merchant-Ops/caojingluan/pinzhihaotuan-mobile.png"
                        ]
                    },
                    "relItemId": 23095789833705,
                    "isAdd": 0,
                    "sellerId": 4153925705,
                    "zkFinalPrice": 1290,
                    "userId": 88187680
                },
                "track": {}
            },
            "SelectionNavBar_RnL9WCRkLi1ONPs0": {
                "instanceId": "SelectionNavBar_RnL9WCRkLi1ONPs0",
                "componentInstanceKey": 732245005,
                "name": "SelectionNavBar",
                "cid": 525,
                "ver": 2,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "mtp_popularizeChart_VUjTj1ITKFawrYmO": {
                "instanceId": "mtp_popularizeChart_VUjTj1ITKFawrYmO",
                "componentInstanceKey": 689245002,
                "name": "mtp_popularizeChart",
                "cid": 649,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            12,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "promoteInfo": {
                        "itemId": 23095789833705,
                        "promoteTitle": "近30天推广数据",
                        "totalOrderNum": 672,
                        "totalPv": 10236,
                        "totalPromoterNum": 431,
                        "promoteList": [
                            {
                                "dateStr": "0630",
                                "orderNum": 27,
                                "pv": 178,
                                "promoterNum": 33
                            },
                            {
                                "dateStr": "0701",
                                "orderNum": 16,
                                "pv": 175,
                                "promoterNum": 23
                            },
                            {
                                "dateStr": "0702",
                                "orderNum": 26,
                                "pv": 239,
                                "promoterNum": 12
                            },
                            {
                                "dateStr": "0703",
                                "orderNum": 19,
                                "pv": 144,
                                "promoterNum": 11
                            },
                            {
                                "dateStr": "0704",
                                "orderNum": 21,
                                "pv": 158,
                                "promoterNum": 19
                            },
                            {
                                "dateStr": "0705",
                                "orderNum": 47,
                                "pv": 237,
                                "promoterNum": 13
                            },
                            {
                                "dateStr": "0706",
                                "orderNum": 16,
                                "pv": 206,
                                "promoterNum": 21
                            },
                            {
                                "dateStr": "0707",
                                "orderNum": 25,
                                "pv": 200,
                                "promoterNum": 16
                            },
                            {
                                "dateStr": "0708",
                                "orderNum": 23,
                                "pv": 181,
                                "promoterNum": 19
                            },
                            {
                                "dateStr": "0709",
                                "orderNum": 22,
                                "pv": 501,
                                "promoterNum": 25
                            },
                            {
                                "dateStr": "0710",
                                "orderNum": 19,
                                "pv": 445,
                                "promoterNum": 15
                            },
                            {
                                "dateStr": "0711",
                                "orderNum": 22,
                                "pv": 454,
                                "promoterNum": 16
                            },
                            {
                                "dateStr": "0712",
                                "orderNum": 17,
                                "pv": 416,
                                "promoterNum": 13
                            },
                            {
                                "dateStr": "0713",
                                "orderNum": 20,
                                "pv": 387,
                                "promoterNum": 17
                            },
                            {
                                "dateStr": "0714",
                                "orderNum": 28,
                                "pv": 490,
                                "promoterNum": 14
                            },
                            {
                                "dateStr": "0715",
                                "orderNum": 13,
                                "pv": 363,
                                "promoterNum": 14
                            },
                            {
                                "dateStr": "0716",
                                "orderNum": 21,
                                "pv": 444,
                                "promoterNum": 12
                            },
                            {
                                "dateStr": "0717",
                                "orderNum": 22,
                                "pv": 438,
                                "promoterNum": 12
                            },
                            {
                                "dateStr": "0718",
                                "orderNum": 21,
                                "pv": 450,
                                "promoterNum": 23
                            },
                            {
                                "dateStr": "0719",
                                "orderNum": 25,
                                "pv": 409,
                                "promoterNum": 20
                            },
                            {
                                "dateStr": "0720",
                                "orderNum": 24,
                                "pv": 490,
                                "promoterNum": 11
                            },
                            {
                                "dateStr": "0721",
                                "orderNum": 28,
                                "pv": 515,
                                "promoterNum": 10
                            },
                            {
                                "dateStr": "0722",
                                "orderNum": 17,
                                "pv": 374,
                                "promoterNum": 15
                            },
                            {
                                "dateStr": "0723",
                                "orderNum": 24,
                                "pv": 463,
                                "promoterNum": 8
                            },
                            {
                                "dateStr": "0724",
                                "orderNum": 36,
                                "pv": 532,
                                "promoterNum": 10
                            },
                            {
                                "dateStr": "0725",
                                "orderNum": 18,
                                "pv": 438,
                                "promoterNum": 5
                            },
                            {
                                "dateStr": "0726",
                                "orderNum": 31,
                                "pv": 431,
                                "promoterNum": 13
                            },
                            {
                                "dateStr": "0727",
                                "orderNum": 44,
                                "pv": 478,
                                "promoterNum": 11
                            },
                            {
                                "dateStr": "0728",
                                "orderNum": 0,
                                "pv": 0,
                                "promoterNum": 0
                            }
                        ]
                    }
                },
                "track": {}
            },
            "mtp_saleInfo_zbTftbzuhdJ8IYMG": {
                "instanceId": "mtp_saleInfo_zbTftbzuhdJ8IYMG",
                "componentInstanceKey": 683245003,
                "name": "mtp_saleInfo",
                "cid": 638,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            12,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "totalStockCount": 1991825,
                    "totalSaleAmount": 702748,
                    "promoterNum": 307379
                },
                "track": {}
            },
            "mtp_videoList_wBLYPatUrLeB7VZs": {
                "instanceId": "mtp_videoList_wBLYPatUrLeB7VZs",
                "componentInstanceKey": 689245001,
                "name": "mtp_videoList",
                "cid": 647,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "videoList": [
                        {
                            "nikename": "宸宇商行",
                            "imgUrl": "https://p2.a.yximgs.com/upic/2025/06/26/09/BMjAyNTA2MjYwOTI5NTlfNDcyMzQwOTE0OF8xNjc3NDg1NjAyMDZfMF8z_Baaa88a6736138b17b00f7c19b7097d5b.jpg?tag=1-1753744232-unknown-0-vj5uborecq-3501c1dded7d7a85&clientCacheKey=3xg4m743nr2656y.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/06/26/09/BMjAyNTA2MjYwOTI5NTlfNDcyMzQwOTE0OF8xNjc3NDg1NjAyMDZfMF8z_b_B8a28387d7e1ce41dd090c27d52299fd0.mp4?tag=1-1753744232-unknown-0-9ex5o8mvek-f8344adac2d1b5b4&clientCacheKey=3xg4m743nr2656y_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 39125,
                            "headImg": "https://p2-ec.ecukwai.com/uhead/AB/2025/04/19/13/BMjAyNTA0MTkxMzI2MDZfNDcyMzQwOTE0OF8yX2hkODUzXzU4MQ==_s.jpg",
                            "fansNum": 1353,
                            "kwaiUrl": "kwai://work/167748560206?userId=4723409148",
                            "gmvOf90": 2809,
                            "authorId": 4723409148
                        },
                        {
                            "nikename": "太原老葛润杨甄选",
                            "imgUrl": "https://p2.a.yximgs.com/upic/2025/05/21/18/BMjAyNTA1MjExODA5NTJfNDY3NDA1ODM0Ml8xNjQ4MTgxMzIwOTZfMl8z_Bd5740648593918394b716f879ec11b5f.jpg?tag=1-1753744232-unknown-0-uh41cfh209-f76a9ec182a677d6&clientCacheKey=3x79g9s3ychwiuk.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/21/18/BMjAyNTA1MjExODA5NTJfNDY3NDA1ODM0Ml8xNjQ4MTgxMzIwOTZfMl8z_b_B4e89db8f3f3468446dae80c8c8dc858d.mp4?tag=1-1753744232-unknown-0-vl6rwudvlv-0fb27325acc2872d&clientCacheKey=3x79g9s3ychwiuk_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 18174,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2025/06/26/22/BMjAyNTA2MjYyMjQ3NTlfNDY3NDA1ODM0Ml8yX2hkOTk1Xzc0MA==_s.jpg",
                            "fansNum": 6225,
                            "kwaiUrl": "kwai://work/164818132096?userId=4674058342",
                            "gmvOf90": 1959,
                            "authorId": 4674058342
                        },
                        {
                            "nikename": "幸福刚刚好！优选好物",
                            "imgUrl": "https://p3.a.yximgs.com/upic/2025/06/30/09/BMjAyNTA2MzAwOTI3MTlfNDYyODA2NzQ2OF8xNjgxMDEyNzA4MDRfMl8z_ev2_B80268433c7d3b53176c661719f1fc7ba.jpg?tag=1-1753744232-unknown-0-ikfdn6dg3b-763617ddc1e5f6a5&clientCacheKey=3x4pf4m9mtahagu_ev2.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/06/30/09/BMjAyNTA2MzAwOTI3MTlfNDYyODA2NzQ2OF8xNjgxMDEyNzA4MDRfMl8z_b_Bfa5c1305801e6cca00b74cbfd63c0ba0.mp4?tag=1-1753744232-unknown-0-x3ytzjmnmn-6d40b4ec17efba55&clientCacheKey=3x4pf4m9mtahagu_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 28785,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2025/06/18/19/BMjAyNTA2MTgxOTUwNTZfNDYyODA2NzQ2OF8yX2hkODY5XzY3Mg==_s.jpg",
                            "fansNum": 118,
                            "kwaiUrl": "kwai://work/168101270804?userId=4628067468",
                            "gmvOf90": 1604,
                            "authorId": 4628067468
                        },
                        {
                            "nikename": "Cuser_4149085016",
                            "imgUrl": "https://p4.a.yximgs.com/upic/2025/05/15/11/BMjAyNTA1MTUxMTI0NDlfNDE0OTA4NTAxNl8xNjQyOTQ0NTUzMjRfMl8z_ev2_B8fcf740089d37ae947a8fffa3d867f38.jpg?tag=1-1753744232-unknown-0-gpcorvugwv-31e31613cd51d8fa&clientCacheKey=3xgzapw7wajj5a4_ev2.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/15/11/BMjAyNTA1MTUxMTI0NDlfNDE0OTA4NTAxNl8xNjQyOTQ0NTUzMjRfMl8z_b_Be523304a46c0dde779a22abea1140599.mp4?tag=1-1753744232-unknown-0-pshh3nlb4c-183ca2cd60b1a496&clientCacheKey=3xgzapw7wajj5a4_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 10531,
                            "headImg": "https://p2-ec.ecukwai.com/s1/i/def/head_u.png",
                            "fansNum": 1312,
                            "kwaiUrl": "kwai://work/164294455324?userId=4149085016",
                            "gmvOf90": 837,
                            "authorId": 4149085016
                        },
                        {
                            "nikename": "爱可可百货店",
                            "imgUrl": "https://p2.a.yximgs.com/upic/2025/06/20/14/BMjAyNTA2MjAxNDIxNTlfMTcxMzg5NjAxNV8xNjcyNjY5MjEwNDNfMF8z_B45895b841302fdc345d64497ba1ee2b8.jpg?tag=1-1753744232-unknown-0-rn8tp9looa-5deaf29f44c1fc56&clientCacheKey=3xekb4cavqut9ri.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/06/20/14/BMjAyNTA2MjAxNDIxNTlfMTcxMzg5NjAxNV8xNjcyNjY5MjEwNDNfMF8z_b_Bf9923a129c35a7f8b23a5703058e55e1.mp4?tag=1-1753744232-unknown-0-sclw4t5x5i-17025d347a8c2288&clientCacheKey=3xekb4cavqut9ri_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 15688,
                            "headImg": "https://p2-ec.ecukwai.com/uhead/AB/2025/07/02/08/BMjAyNTA3MDIwODQ1NDRfMTcxMzg5NjAxNV8yX2hkNjA4XzkwNQ==_s.jpg",
                            "fansNum": 2162,
                            "kwaiUrl": "kwai://work/167266921043?userId=1713896015",
                            "gmvOf90": 809,
                            "authorId": 1713896015
                        },
                        {
                            "nikename": "幸福刚刚好！优选好物",
                            "imgUrl": "https://p1.a.yximgs.com/upic/2025/07/09/13/BMjAyNTA3MDkxMzQ3MjNfNDYyODA2NzQ2OF8xNjg5NTIzNTIyNjlfMF8z_B82b7eea5a146b414b7746943fd82e4a1.jpg?tag=1-1753744232-unknown-0-jukhpc5dhc-6648a14ce55c9255&clientCacheKey=3xhi5hw79abfbm2.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://txmov2.a.kwimgs.com/upic/2025/07/09/13/BMjAyNTA3MDkxMzQ3MjNfNDYyODA2NzQ2OF8xNjg5NTIzNTIyNjlfMF8z_b_B446130977b8b2a76b35a71ac9a6ed1b3.mp4?tag=1-1753744232-unknown-0-smyohgfxrj-1b410305d1cb2de3&clientCacheKey=3xhi5hw79abfbm2_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 8409,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2025/06/18/19/BMjAyNTA2MTgxOTUwNTZfNDYyODA2NzQ2OF8yX2hkODY5XzY3Mg==_s.jpg",
                            "fansNum": 118,
                            "kwaiUrl": "kwai://work/168952352269?userId=4628067468",
                            "gmvOf90": 707,
                            "authorId": 4628067468
                        },
                        {
                            "nikename": "Cuser_3422136907",
                            "imgUrl": "https://p5.a.yximgs.com/upic/2025/04/29/11/BMjAyNTA0MjkxMTI4MTNfMzQyMjEzNjkwN18xNjI4OTkwMjg0MThfMl8z_ev3_Be8e4404c088d8549a0a4f754aa200cbd.jpg?tag=1-1753744232-unknown-0-sktxqpgmp9-03f182e84038d5cb&clientCacheKey=3xnnkbgsp4ntcum_ev3.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/04/29/11/BMjAyNTA0MjkxMTI4MTNfMzQyMjEzNjkwN18xNjI4OTkwMjg0MThfMl8z_b_B6b7862a97f7dca41c64e51dd7b5fa827.mp4?tag=1-1753744232-unknown-0-waxymuptqa-0fd6cce4ae083506&clientCacheKey=3xnnkbgsp4ntcum_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 7450,
                            "headImg": "https://p2-ec.ecukwai.com/s1/i/def/head_u.png",
                            "fansNum": 70,
                            "kwaiUrl": "kwai://work/162899028418?userId=3422136907",
                            "gmvOf90": 698,
                            "authorId": 3422136907
                        },
                        {
                            "nikename": "瑶瑶优选好物",
                            "imgUrl": "https://p3.a.yximgs.com/upic/2025/06/12/17/BMjAyNTA2MTIxNzIwMzBfMzExOTI4NDIxNV8xNjY2Mzc0MzM2MjRfMl8z_B45bbc9eb45b6b999ff6df578db7be188.jpg?tag=1-1753744232-unknown-0-5n7lxvgm3q-2424a8b95f997858&clientCacheKey=3xjzcba95jppk6m.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/06/12/17/BMjAyNTA2MTIxNzIwMzBfMzExOTI4NDIxNV8xNjY2Mzc0MzM2MjRfMl8z_b_B1dd87d36a6d31b618dedcc0c42916b46.mp4?tag=1-1753744232-unknown-0-3zl2jiuvkw-3f8213aaa5a38aac&clientCacheKey=3xjzcba95jppk6m_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 7841,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2025/05/27/18/BMjAyNTA1MjcxODMyMjRfMzExOTI4NDIxNV8yX2hkMjk2XzQ4_s.jpg",
                            "fansNum": 300,
                            "kwaiUrl": "kwai://work/166637433624?userId=3119284215",
                            "gmvOf90": 500,
                            "authorId": 3119284215
                        }
                    ],
                    "relItemId": 23095789833705,
                    "itemTitle": "12.9元抢500g/袋俄罗斯风味混合糖巧克力多种口味夹心软牛扎糖果",
                    "bestCommissionId": 1517207979705,
                    "bestCommissionType": 6,
                    "isAdd": 0,
                    "sliceList": [
                        {
                            "nikename": "二马捡漏号",
                            "imgUrl": "https://p2.a.yximgs.com/upic/2025/05/25/12/BMjAyNTA1MjUxMjExNDRfMjY3ODE3ODc4XzE2NTEyODQ1NTU0Ml8wXzM=_B8664f6a46f1c67ce014170af3bba58e0.jpg?tag=1-1753744232-unknown-0-y5ajel9qtw-75c9c0b6f87a70bb&clientCacheKey=3x22efederp46es.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/25/12/BMjAyNTA1MjUxMjExNDRfMjY3ODE3ODc4XzE2NTEyODQ1NTU0Ml8wXzM=_b_B5988d6d1848aa2f8aaf6caf50df8574d.mp4?tag=1-1753744232-unknown-0-chdiqbrnxt-521146cf3d9b52e9&clientCacheKey=3x22efederp46es_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 1541,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2022/06/02/14/BMjAyMjA2MDIxNDI2MzhfMjY3ODE3ODc4XzJfaGQ1MzRfNzk=_s.jpg",
                            "authorId": 267817878
                        },
                        {
                            "nikename": "吴新华精特果蔬配送中心",
                            "imgUrl": "https://p2.a.yximgs.com/upic/2025/05/20/09/BMjAyNTA1MjAwOTM5MTZfODU2Mjc4MTczXzE2NDcxNTg0NDI1MV8wXzM=_B414e4fa1052dca303b7ead0f838c6f3b.jpg?tag=1-1753744232-unknown-0-gj5tjz1zsl-04ed845bf2d522db&clientCacheKey=3xvntr7xcygzmjk.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/20/09/BMjAyNTA1MjAwOTM5MTZfODU2Mjc4MTczXzE2NDcxNTg0NDI1MV8wXzM=_b_B8391eed3db539ebbd4c92a54fce1b0f4.mp4?tag=1-1753744232-unknown-0-hid05nsaxg-5e881149d2c43557&clientCacheKey=3xvntr7xcygzmjk_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 691,
                            "headImg": "https://p2-ec.ecukwai.com/uhead/AB/2021/11/03/23/BMjAyMTExMDMyMzA3MzJfODU2Mjc4MTczXzJfaGQyOF8zMDQ=_s.jpg",
                            "authorId": 856278173
                        },
                        {
                            "nikename": "大连姐",
                            "imgUrl": "https://p1.a.yximgs.com/upic/2025/05/17/10/BMjAyNTA1MTcxMDAxMTRfODQyMjIyNzMyXzE2NDQ0ODU4NjIzM18wXzM=_Ba198966a7cf648b4a2fbff513e690c43.jpg?tag=1-1753744232-unknown-0-m0siiddnug-3d2199a3045c86bf&clientCacheKey=3x45azvdx4dc9es.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://txmov2.a.kwimgs.com/upic/2025/05/17/10/BMjAyNTA1MTcxMDAxMTRfODQyMjIyNzMyXzE2NDQ0ODU4NjIzM18wXzM=_b_Be7a2cfc652060646f1e1afbca7c8e341.mp4?tag=1-1753744232-unknown-0-z9z0h5xoqf-038ddffa458942fd&clientCacheKey=3x45azvdx4dc9es_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 683,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2025/07/26/22/BMjAyNTA3MjYyMjIxMTFfODQyMjIyNzMyXzJfaGQ4MTBfODk3_s.jpg",
                            "authorId": 842222732
                        },
                        {
                            "nikename": "皮草小哥",
                            "imgUrl": "https://p5.a.yximgs.com/upic/2025/05/15/23/BMjAyNTA1MTUyMzQwMTBfMzgzMTY0NzE4M18xNjQzNDQ1OTc0OThfMF8z_B13694e4565c63f911035beb08c938cfd.jpg?tag=1-1753744232-unknown-0-a58ig5h5dh-ea998591685b29c1&clientCacheKey=3x7miur9t49a9z4.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/15/23/BMjAyNTA1MTUyMzQwMTBfMzgzMTY0NzE4M18xNjQzNDQ1OTc0OThfMF8z_b_Bacefb83082767b17f688c1b2d0e465f0.mp4?tag=1-1753744232-unknown-0-zmclwnbimy-0b562ff367ebf028&clientCacheKey=3x7miur9t49a9z4_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 591,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2023/12/11/10/BMjAyMzEyMTExMDEwNTRfMzgzMTY0NzE4M18xX2hkODkxXzU5_s.jpg",
                            "authorId": 3831647183
                        },
                        {
                            "nikename": "皮草小哥",
                            "imgUrl": "https://p1.a.yximgs.com/upic/2025/05/10/22/BMjAyNTA1MTAyMjIxMDlfMzgzMTY0NzE4M18xNjM5NTUyNDkwNjRfMF8z_Bcdf5158195d2ce71fbed6be3dd771442.jpg?tag=1-1753744232-unknown-0-dveljo6jua-40e58c874a365743&clientCacheKey=3x6f6ictweg3cx6.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://txmov2.a.kwimgs.com/upic/2025/05/10/22/BMjAyNTA1MTAyMjIxMDlfMzgzMTY0NzE4M18xNjM5NTUyNDkwNjRfMF8z_b_B220f10be9843017b22abf3488aa1b409.mp4?tag=1-1753744232-unknown-0-cta10y0udq-4b1ef425b672b684&clientCacheKey=3x6f6ictweg3cx6_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 544,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2023/12/11/10/BMjAyMzEyMTExMDEwNTRfMzgzMTY0NzE4M18xX2hkODkxXzU5_s.jpg",
                            "authorId": 3831647183
                        },
                        {
                            "nikename": "皮草小哥",
                            "imgUrl": "https://p3.a.yximgs.com/upic/2025/05/14/22/BMjAyNTA1MTQyMjQwNDBfMzgzMTY0NzE4M18xNjQyNjk5MDc5NjdfMF8z_B544fe7517771f49cf2c89fcde9e52c44.jpg?tag=1-1753744232-unknown-0-xcpfieqsxx-d91f9a414ab6ba60&clientCacheKey=3x39wbzkgefu2uq.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/14/22/BMjAyNTA1MTQyMjQwNDBfMzgzMTY0NzE4M18xNjQyNjk5MDc5NjdfMF8z_b_B42bf370e4ee6f60c281cc0b81fb74cef.mp4?tag=1-1753744232-unknown-0-grbz1wthxw-11ef61548b1947c5&clientCacheKey=3x39wbzkgefu2uq_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 535,
                            "headImg": "https://p2-ec.ecukwai.com/uhead/AB/2023/12/11/10/BMjAyMzEyMTExMDEwNTRfMzgzMTY0NzE4M18xX2hkODkxXzU5_s.jpg",
                            "authorId": 3831647183
                        },
                        {
                            "nikename": "皮草小哥",
                            "imgUrl": "https://p1.a.yximgs.com/upic/2025/05/16/23/BMjAyNTA1MTYyMzIyNDdfMzgzMTY0NzE4M18xNjQ0MjQ0NzQwNDBfMF8z_Bd55b09f9658affd940f474b8137683aa.jpg?tag=1-1753744232-unknown-0-fyulog6nve-422fe868e0b3a2b9&clientCacheKey=3x5uhsaa37jfmw9.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://txmov2.a.kwimgs.com/upic/2025/05/16/23/BMjAyNTA1MTYyMzIyNDdfMzgzMTY0NzE4M18xNjQ0MjQ0NzQwNDBfMF8z_b_B90041a1a2dc07f2b61dd8a2a11cdfde2.mp4?tag=1-1753744232-unknown-0-osl6cy2obe-93cf3cbf5499987b&clientCacheKey=3x5uhsaa37jfmw9_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 505,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2023/12/11/10/BMjAyMzEyMTExMDEwNTRfMzgzMTY0NzE4M18xX2hkODkxXzU5_s.jpg",
                            "authorId": 3831647183
                        },
                        {
                            "nikename": "皮草小哥",
                            "imgUrl": "https://p2.a.yximgs.com/upic/2025/05/16/22/BMjAyNTA1MTYyMjM3MjNfMzgzMTY0NzE4M18xNjQ0MjA3MzU0MDhfMF8z_Be52e1c73aa8dbb352f5fd025b9cae1c3.jpg?tag=1-1753744232-unknown-0-xdgqbegprc-934385b6f267d9f6&clientCacheKey=3xgahdujyb3urcq.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/16/22/BMjAyNTA1MTYyMjM3MjNfMzgzMTY0NzE4M18xNjQ0MjA3MzU0MDhfMF8z_b_B54f5affc09ee3e07dda5dc7fd089c18f.mp4?tag=1-1753744232-unknown-0-cbixydxe2i-4c729f3b66d0de2a&clientCacheKey=3xgahdujyb3urcq_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 502,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2023/12/11/10/BMjAyMzEyMTExMDEwNTRfMzgzMTY0NzE4M18xX2hkODkxXzU5_s.jpg",
                            "authorId": 3831647183
                        },
                        {
                            "nikename": "皮草小哥",
                            "imgUrl": "https://p2.a.yximgs.com/upic/2025/05/17/22/BMjAyNTA1MTcyMjA0MDZfMzgzMTY0NzE4M18xNjQ1MjIwMjMyNTVfMF8z_Bce177d32a31dd0244a6f0d89f4b16342.jpg?tag=1-1753744232-unknown-0-vvb2annxts-a336aa696521647d&clientCacheKey=3xi9efu4id33a92.jpg&bp=10000&kwai-not-alloc=35&ss=vps",
                            "videoUrl": "https://alimov2.a.kwimgs.com/upic/2025/05/17/22/BMjAyNTA1MTcyMjA0MDZfMzgzMTY0NzE4M18xNjQ1MjIwMjMyNTVfMF8z_b_B40dc7eeda949e479f77315e1088e56bb.mp4?tag=1-1753744232-unknown-0-lnpt4oeeqt-57166e62228d607e&clientCacheKey=3xi9efu4id33a92_b.mp4&bp=10000&kwai-not-alloc=35&tt=b&ss=vps",
                            "viewCount": 491,
                            "headImg": "https://p4-ec.ecukwai.com/uhead/AB/2023/12/11/10/BMjAyMzEyMTExMDEwNTRfMzgzMTY0NzE4M18xX2hkODkxXzU5_s.jpg",
                            "authorId": 3831647183
                        }
                    ]
                },
                "track": {}
            },
            "infinity_root_container_t5vzLeOoxkpBfVli": {
                "instanceId": "infinity_root_container_t5vzLeOoxkpBfVli",
                "componentInstanceKey": 675245006,
                "name": "infinity_root_container",
                "cid": 528,
                "ver": 4,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "relItemId": 23095789833705,
                    "shopId": 4153925705,
                    "isAdd": 0,
                    "recoReasonInfoList": [
                        {
                            "name": "N天降价",
                            "desc": "30天降8元"
                        },
                        {
                            "name": "近x天佣金率稳定",
                            "desc": "近15天佣金率稳定"
                        },
                        {
                            "name": "N人评价",
                            "desc": "2899人评价“质量好”"
                        }
                    ],
                    "hasRecoReason": true,
                    "zkFinalPrice": 1290,
                    "commissionRateStr": "16",
                    "bestCommissionId": 1517207979705,
                    "bestCommissionType": 6,
                    "webLogParam": {
                        "kwaiselectMode": 0
                    }
                },
                "track": {}
            },
            "scroll_node_gZEmI9AekSIYcGFu": {
                "instanceId": "scroll_node_gZEmI9AekSIYcGFu",
                "componentInstanceKey": 712245008,
                "name": "scroll_node",
                "cid": 713,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            90,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "mtp_card_wzBrvcynmDdyrf70": {
                "instanceId": "mtp_card_wzBrvcynmDdyrf70",
                "componentInstanceKey": 681245005,
                "name": "mtp_card",
                "cid": 655,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            12,
                            19,
                            12,
                            19
                        ],
                        "margin": [
                            0,
                            0,
                            8,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            }
        },
        "hierarchy": {
            "root": "KUAISHOU_ERA_ROOT",
            "structure": {
                "KUAISHOU_ERA_ROOT": [
                    "infinity_root_container_t5vzLeOoxkpBfVli",
                    "mtp_decisionInfo_bottom_qkcyahWVmM0XV3yZ",
                    "mtp_decision_material_guide_yHZFiUq8HKxhAgyD"
                ],
                "infinity_root_container_t5vzLeOoxkpBfVli": [
                    "scroll_node_gZEmI9AekSIYcGFu"
                ],
                "scroll_node_gZEmI9AekSIYcGFu": [
                    "SelectionNavBar_RnL9WCRkLi1ONPs0",
                    "mtp_headImage_sVBoVmgR43OV5Cb1",
                    "mtp_card_CgEg27LO0OW2ypP7",
                    "mtp_card_wzBrvcynmDdyrf70",
                    "mtp_buyReturn_dtzrC2FZqDnLGYw1",
                    "mtp_sellerPoint_DgBMHwnGrd5w59c1",
                    "mtp_storeEntrance_wl8b7ckVR4C9To2A",
                    "mtp_goodsEvaluate_GYeZHNsw01o26OSU",
                    "exemptReview_IxGUtDDDoMoENxqB",
                    "mtp_videoList_wBLYPatUrLeB7VZs",
                    "mtp_topSaleCardList_6WIwyvODIfJxhW0g",
                    "mtp_popularizeChart_VUjTj1ITKFawrYmO",
                    "mtp_merchantsPromotion_xwUb6DrWfomKeMDK"
                ],
                "mtp_card_CgEg27LO0OW2ypP7": [
                    "mtp_goodsTitle_OOi3gcD1ntWEHjWZ",
                    "mtp_recommendedTags_UDc5OUuKBryJq4SH",
                    "ActivityPriceCard_pOuFpShaJPfTXoC0",
                    "mtp_detail_info_container_ilBeuZBTjMeYrSNb",
                    "mtp_stepCommission_F4KZDOJuVt1VcSPQ",
                    "mtp_saleInfo_zbTftbzuhdJ8IYMG",
                    "mtp_tagList_mHvs4GHsmv1CVQak",
                    "mtp_hotRankInfo_h3fROxMTqvkkiCPW"
                ],
                "mtp_card_wzBrvcynmDdyrf70": [
                    "mtp_trustIcon_HpSlZgWMpY9Q6ycj",
                    "mtp_express_H9CWlNDqrZSdp4UB"
                ],
                "mtp_detail_info_container_ilBeuZBTjMeYrSNb": [
                    "mtp_priceInfo_A2Kt9FqkvJOPz3Ye",
                    "mtp_commission_BJK17v4ZBSVYkWul"
                ]
            }
        },
        "global": {
            "protocolVersion": 2,
            "commonData": {
                "switchMap": {
                    "isShowScrollNode": true
                }
            },
            "pageName": "CpsSelectionDecisionInfo",
            "pageVersion": 74
        }
    },
    "requestId": "753744239205983105"
}

# C接口
{
    "result": 1,
    "error_msg": "SUCCESS",
    "data": {
        "component": [
            {
                "name": "CpsCommonSearch",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 2,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 1063
            },
            {
                "name": "search_tab",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 1075
            },
            {
                "name": "CpsGoodsCard",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 1066
            },
            {
                "name": "search_goodsList",
                "renderType": "RN",
                "type": "viewNormal",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 1079
            },
            {
                "name": "mtp_filter",
                "renderType": "RN",
                "type": "viewGroup",
                "ver": 1,
                "bundleUrl": "",
                "downgradeName": "",
                "cid": 535
            },
            {
                "name": "infinity_root_node",
                "renderType": "NATIVE",
                "type": "viewNormal",
                "ver": 1,
                "cid": 0
            }
        ],
        "data": {
            "KUAISHOU_ERA_ROOT": {
                "instanceId": "KUAISHOU_ERA_ROOT",
                "componentInstanceKey": 1001,
                "name": "infinity_root_node",
                "cid": 0,
                "ver": 1,
                "style": {},
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "search_tab": {
                "instanceId": "search_tab",
                "componentInstanceKey": 976245002,
                "name": "search_tab",
                "cid": 1075,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "strategy": "overwrite",
                "track": {}
            },
            "CpsGoodsCard_RwyFX2mqnUP79fvu": {
                "instanceId": "CpsGoodsCard_RwyFX2mqnUP79fvu",
                "componentInstanceKey": 974245002,
                "name": "CpsGoodsCard",
                "cid": 1066,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "type": 0,
                    "jumpUrl": "https://app.kwaixiaodian.com/page/kwaishop-cps-selection-decision-app?layoutType=4&entranceScene=accurateSearch&relItemId=24323124788765",
                    "position": 0,
                    "configName": "normalVerticalGoodsCardStyle",
                    "relItemId": 24323124788765,
                    "itemImgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-1491014765-360f780d80734c2eb5aacb17f122e642.jpg",
                    "itemTitle": "20大包巨无霸抽纸大包大号加厚抽纸实惠装家用抽纸整箱",
                    "sellerId": 1491014765,
                    "sellerName": "桔子树纸品家居",
                    "soldCountThirtyDays": 222825,
                    "reservePrice": "16.80",
                    "zkFinalPrice": "16.80",
                    "commissionRate": "10",
                    "profitAmount": "1.68",
                    "bestCommissionId": 1871159762765,
                    "bestCommissionType": 6,
                    "isAdd": 0,
                    "promoterCount": 84067,
                    "sourceType": 99,
                    "relVideo": null,
                    "relLive": null,
                    "stepCommissionInfo": {
                        "stepNum": 0,
                        "beforeStepProfitAmount": "",
                        "beforeStepCommissionRate": "",
                        "afterStepProfitAmount": "",
                        "afterStepCommissionRate": "",
                        "startTime": 0,
                        "endTime": 0
                    },
                    "rankInfo": {
                        "rankText": "入选商用纸爆款榜",
                        "rankLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-galaxy-app/landing/category?layoutType=4&configurationId=6&tag=distribute_leaf_category_rank_3046&trackRankType=2"
                    },
                    "itemTagDto": [
                        {
                            "tagCode": "distribute_send_ensure",
                            "tagDesc": "发货保障",
                            "tagValue": "1",
                            "tagImgUrl": null,
                            "fireFlag": false
                        },
                        {
                            "tagCode": "distribute_sample_rule",
                            "tagDesc": "买样后返",
                            "tagValue": "1",
                            "tagImgUrl": null,
                            "fireFlag": false
                        },
                        {
                            "tagCode": "distribute_return_back_freight",
                            "tagDesc": "退货补运费",
                            "tagValue": "1",
                            "tagImgUrl": null,
                            "fireFlag": false
                        },
                        {
                            "tagCode": "distribute_fast_refund",
                            "tagDesc": "极速退款",
                            "tagValue": "1",
                            "tagImgUrl": null,
                            "fireFlag": false
                        }
                    ],
                    "titleTagDto": [],
                    "recoReason": [
                        {
                            "name": "新品首推",
                            "desc": "新品首推"
                        }
                    ],
                    "exposureWeightType": 0,
                    "activityType": 0,
                    "waistCoverShowInfo": {
                        "waistName": "",
                        "waistImgUrl": "",
                        "discountPrice": "",
                        "discountDefaultText": "",
                        "discountBgColor": "",
                        "themeUrl": "",
                        "themeSceneId": 0,
                        "tagId": 0
                    },
                    "sellingPointList": [
                        {
                            "desc": "新品首推",
                            "font": "#FF7031",
                            "type": 1
                        },
                        {
                            "desc": "发货保障",
                            "font": "#FE3665",
                            "type": 2
                        },
                        {
                            "desc": "买样后返",
                            "font": "#FE3665",
                            "type": 2
                        },
                        {
                            "desc": "退货补运费",
                            "font": "#FE3665",
                            "type": 2
                        },
                        {
                            "desc": "极速退款",
                            "font": "#FE3665",
                            "type": 2
                        }
                    ],
                    "textLink": null,
                    "serverExpTag": "1_u/52790558751826_mr999999",
                    "webLogParam": {
                        "kwaiselectMode": 0,
                        "distrLiveDistorType": "other",
                        "promoterWorthType": "UNKNOWN",
                        "requestId": "b90e818a-809a-469a-b69a-d5da0b1c093e"
                    },
                    "extMap": {
                        "bizCode": "H5_SELECTION_CENTER",
                        "isHotSale": "0",
                        "investmentActivityStatus": "0",
                        "promoterHeadIconList": "[]",
                        "shopText": "",
                        "investmentActivityId": "0",
                        "dailyCommission": "0",
                        "scene": "recoKeywordSearch",
                        "activityOrderNum": "0",
                        "serverExpTag": "1_u/52790558751826_mr999999",
                        "shopLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-shop-goods-app?layoutType=4&shopId=1491014765&shopName=",
                        "matchScene": "accurateSearch",
                        "itemSellerName": "桔子树纸品家居",
                        "decisionPageUrl": "https://app.kwaixiaodian.com/page/kwaishop-cps-selection-decision-app?layoutType=4&entranceScene=accurateSearch&relItemId=24323124788765"
                    },
                    "ext": {
                        "bizCode": "H5_SELECTION_CENTER",
                        "isHotSale": "0",
                        "investmentActivityStatus": "0",
                        "promoterHeadIconList": "[]",
                        "shopText": "",
                        "investmentActivityId": "0",
                        "dailyCommission": "0",
                        "scene": "recoKeywordSearch",
                        "activityOrderNum": "0",
                        "serverExpTag": "1_u/52790558751826_mr999999",
                        "shopLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-shop-goods-app?layoutType=4&shopId=1491014765&shopName=",
                        "matchScene": "accurateSearch",
                        "itemSellerName": "桔子树纸品家居",
                        "decisionPageUrl": "https://app.kwaixiaodian.com/page/kwaishop-cps-selection-decision-app?layoutType=4&entranceScene=accurateSearch&relItemId=24323124788765"
                    },
                    "promoterNum": 84067,
                    "linkType": 0,
                    "activityId": 0,
                    "secondActivityId": 0,
                    "activityUserId": 0,
                    "activityUserIcon": null,
                    "activityUserName": null,
                    "shopLink": "https://app.kwaixiaodian.com/web/kwaishop-cps-shop-goods-app?layoutType=4&shopId=1491014765&shopName=",
                    "shopText": ""
                },
                "track": {}
            },
            "common_search": {
                "instanceId": "common_search",
                "componentInstanceKey": 978245001,
                "name": "CpsCommonSearch",
                "cid": 1063,
                "ver": 2,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "track": {}
            },
            "search_goodList": {
                "instanceId": "search_goodList",
                "componentInstanceKey": 976245004,
                "name": "search_goodsList",
                "cid": 1079,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {},
                "strategy": "overwrite",
                "track": {}
            },
            "search_mtp_filter": {
                "instanceId": "search_mtp_filter",
                "componentInstanceKey": 997245004,
                "name": "mtp_filter",
                "cid": 535,
                "ver": 1,
                "style": {
                    "layout": {
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ],
                        "margin": [
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                },
                "engineConfig": {},
                "fields": {
                    "filterInfo": [
                        {
                            "name": "综合排序",
                            "val": "0",
                            "subList": [
                                {
                                    "name": "综合排序",
                                    "val": "0"
                                },
                                {
                                    "name": "上架达人数降序",
                                    "val": "7"
                                }
                            ]
                        },
                        {
                            "name": "销量",
                            "val": "6"
                        },
                        {
                            "name": "佣金率",
                            "val": "4"
                        }
                    ],
                    "tagFilterInfo": [
                        {
                            "title": "券后区间（元）",
                            "searchKey": "price",
                            "subList": [
                                {
                                    "name": "最低价",
                                    "val": "priceStart"
                                },
                                {
                                    "name": "最高价",
                                    "val": "priceEnd"
                                }
                            ],
                            "isMulti": false,
                            "type": "range"
                        },
                        {
                            "title": "佣金率区间",
                            "searchKey": "rate",
                            "subList": [
                                {
                                    "name": "最低值",
                                    "val": "rateStart",
                                    "unit": "%"
                                },
                                {
                                    "name": "最高值",
                                    "val": "rateEnd",
                                    "unit": "%"
                                }
                            ],
                            "isMulti": false,
                            "type": "range"
                        },
                        {
                            "title": "全部分类",
                            "searchKey": "channelId",
                            "subList": [
                                {
                                    "name": "食品饮料",
                                    "val": "3"
                                },
                                {
                                    "name": "家居百货",
                                    "val": "9"
                                },
                                {
                                    "name": "女装女鞋",
                                    "val": "1"
                                },
                                {
                                    "name": "美妆护肤",
                                    "val": "118"
                                },
                                {
                                    "name": "个护清洁",
                                    "val": "6"
                                },
                                {
                                    "name": "医疗保健",
                                    "val": "114"
                                },
                                {
                                    "name": "母婴玩具",
                                    "val": "13"
                                },
                                {
                                    "name": "茶酒生鲜",
                                    "val": "116"
                                },
                                {
                                    "name": "男装男鞋",
                                    "val": "8"
                                },
                                {
                                    "name": "运动户外",
                                    "val": "10"
                                },
                                {
                                    "name": "数码家电",
                                    "val": "4"
                                },
                                {
                                    "name": "珠宝配饰",
                                    "val": "120"
                                }
                            ],
                            "isMulti": false
                        }
                    ]
                },
                "track": {}
            }
        },
        "hierarchy": {
            "root": "KUAISHOU_ERA_ROOT",
            "structure": {
                "KUAISHOU_ERA_ROOT": [
                    "common_search",
                    "search_tab",
                    "search_mtp_filter",
                    "search_goodList"
                ],
                "search_goodList": [
                    "CpsGoodsCard_RwyFX2mqnUP79fvu"
                ]
            }
        },
        "global": {
            "protocolVersion": 2,
            "commonData": {
                "keyWord": "24323124788765",
                "pcursor": "20",
                "judgeType": null,
                "shopQueryV2Switch": true,
                "photoSearchSwitch": true,
                "photoSearchClientVersion": "12.7.10",
                "sessionId": "d307af46-2d36-4ba5-a18c-17bd03e8f2d5",
                "searchResultType": 1,
                "searchResultRecoSwitch": true,
                "configMap": {
                    "normalVerticalGoodsCardStyle": {
                        "id": "CardContainer",
                        "namespace": "@es/kprom-cps-dynamic-goods-card:CardContainer",
                        "props": {
                            "$componentVisible": true,
                            "style": {
                                "width": "192px",
                                "backgroundColor": "#FFFFFF"
                            }
                        },
                        "children": [
                            {
                                "id": "CommonContainer@Image",
                                "namespace": "@es/kprom-cps-dynamic-goods-card:CommonContainer",
                                "props": {
                                    "$componentVisible": true,
                                    "style": {
                                        "position": "relative"
                                    }
                                },
                                "children": [
                                    {
                                        "id": "GoodsImage",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:GoodsImage",
                                        "props": {
                                            "$componentVisible": true,
                                            "imgWidth": 192,
                                            "imgHeight": 192,
                                            "style": {}
                                        },
                                        "fieldKeys": [
                                            "itemImgUrl@url"
                                        ],
                                        "renderer": {}
                                    },
                                    {
                                        "id": "TopHotInfo",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:TopHotInfo",
                                        "props": {
                                            "$componentVisible": true,
                                            "style": {
                                                "position": "absolute",
                                                "bottom": "-1px"
                                            },
                                            "isLinkAtmosphereLabel": true
                                        },
                                        "fieldKeys": [
                                            "rankInfo@rankInfo",
                                            "relItemId@relItemId",
                                            "waistCoverShowInfo@waistCoverShowInfo"
                                        ],
                                        "renderer": {}
                                    },
                                    {
                                        "id": "AtmosphereLabel",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:AtmosphereLabel",
                                        "props": {
                                            "$componentVisible": true,
                                            "style": {
                                                "position": "absolute",
                                                "bottom": "-1px"
                                            }
                                        },
                                        "fieldKeys": [
                                            "waistCoverShowInfo@waistCoverShowInfo"
                                        ],
                                        "renderer": {}
                                    }
                                ],
                                "fieldKeys": [],
                                "renderer": {}
                            },
                            {
                                "id": "CommonContainer@content",
                                "namespace": "@es/kprom-cps-dynamic-goods-card:CommonContainer",
                                "props": {
                                    "$componentVisible": true,
                                    "style": {
                                        "padding": "8px"
                                    }
                                },
                                "children": [
                                    {
                                        "id": "Title",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:Title",
                                        "props": {
                                            "$componentVisible": true,
                                            "numberOfLines": 1,
                                            "titleHeight": 18,
                                            "ellipsizeMode": "clip",
                                            "style": {}
                                        },
                                        "fieldKeys": [
                                            "itemTitle@title",
                                            "titleTagDto@titleTagDto",
                                            "relItemId@relItemId"
                                        ],
                                        "renderer": {}
                                    },
                                    {
                                        "id": "SellingPoint",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:SellingPoint",
                                        "props": {
                                            "$componentVisible": true,
                                            "style": {
                                                "marginTop": "5px"
                                            }
                                        },
                                        "fieldKeys": [
                                            "sellingPointList@sellingPointList",
                                            "relItemId@relItemId"
                                        ],
                                        "renderer": {}
                                    },
                                    {
                                        "id": "CommonContainer@priceInfo",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:CommonContainer",
                                        "props": {
                                            "$componentVisible": true,
                                            "style": {
                                                "flexDirection": "row",
                                                "flexWrap": "wrap",
                                                "alignItems": "center",
                                                "marginTop": "4px"
                                            }
                                        },
                                        "children": [
                                            {
                                                "id": "Commission",
                                                "namespace": "@es/kprom-cps-dynamic-goods-card:Commission",
                                                "props": {
                                                    "$componentVisible": true,
                                                    "style": {
                                                        "marginRight": "4px"
                                                    }
                                                },
                                                "fieldKeys": [
                                                    "profitAmount@profitAmount",
                                                    "stepCommissionInfo@stepCommissionInfo"
                                                ],
                                                "renderer": {}
                                            },
                                            {
                                                "id": "CommissionRate",
                                                "namespace": "@es/kprom-cps-dynamic-goods-card:CommissionRate",
                                                "props": {
                                                    "$componentVisible": true,
                                                    "style": {},
                                                    "layoutType": "vertical"
                                                },
                                                "fieldKeys": [
                                                    "stepCommissionInfo@stepCommissionInfo",
                                                    "commissionRate@commissionRate"
                                                ],
                                                "renderer": {}
                                            }
                                        ],
                                        "fieldKeys": [
                                            "itemImgUrl@url"
                                        ],
                                        "renderer": {}
                                    },
                                    {
                                        "id": "CommonContainer@shopInfo",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:CommonContainer",
                                        "props": {
                                            "$componentVisible": true,
                                            "style": {
                                                "flexDirection": "row",
                                                "marginTop": "4px",
                                                "alignItems": "center",
                                                "justifyContent": "flex-start",
                                                "textAlignVertical": "center"
                                            }
                                        },
                                        "children": [
                                            {
                                                "id": "FinalPrice",
                                                "namespace": "@es/kprom-cps-dynamic-goods-card:FinalPrice",
                                                "props": {
                                                    "$componentVisible": true,
                                                    "style": {
                                                        "alignItems": "center",
                                                        "marginRight": "6px"
                                                    },
                                                    "finalPreFixStyle": {
                                                        "fontSize": "14px",
                                                        "fontWeight": "500"
                                                    },
                                                    "priceStyle": {
                                                        "fontSize": "12px",
                                                        "marginTop": "1px"
                                                    }
                                                },
                                                "fieldKeys": [
                                                    "zkFinalPrice@zkFinalPrice"
                                                ],
                                                "renderer": {}
                                            },
                                            {
                                                "id": "SoldCount",
                                                "namespace": "@es/kprom-cps-dynamic-goods-card:SoldCount",
                                                "props": {
                                                    "$componentVisible": true,
                                                    "style": {
                                                        "marginTop": "1px"
                                                    },
                                                    "soldCountStyle": {
                                                        "fontSize": "11px"
                                                    },
                                                    "isLinkPromoterNum": true
                                                },
                                                "fieldKeys": [
                                                    "promoterCount@promoterNum",
                                                    "soldCountThirtyDays@soldCountThirtyDays"
                                                ],
                                                "renderer": {}
                                            },
                                            {
                                                "id": "PromoterNum",
                                                "namespace": "@es/kprom-cps-dynamic-goods-card:PromoterNum",
                                                "props": {
                                                    "$componentVisible": true,
                                                    "style": {
                                                        "marginTop": "1px"
                                                    },
                                                    "promoterStyle": {
                                                        "fontSize": "11px"
                                                    },
                                                    "isLinkSoldCount": true
                                                },
                                                "fieldKeys": [
                                                    "promoterCount@promoterNum",
                                                    "soldCountThirtyDays@soldCountThirtyDays"
                                                ],
                                                "renderer": {}
                                            }
                                        ],
                                        "fieldKeys": [],
                                        "renderer": {}
                                    },
                                    {
                                        "id": "Buttons",
                                        "namespace": "@es/kprom-cps-dynamic-goods-card:Buttons",
                                        "props": {
                                            "$componentVisible": true,
                                            "style": {
                                                "marginTop": "8px"
                                            },
                                            "verticalType": true
                                        },
                                        "fieldKeys": [
                                            "relItemId@relItemId",
                                            "isAdd@isAdd",
                                            "bestCommissionId@bestCommissionId",
                                            "bestCommissionType@bestCommissionType",
                                            "zkFinalPrice@zkFinalPrice",
                                            "commissionRate@commissionRate",
                                            "activityId@activityId",
                                            "channelId@channelId",
                                            "itemTagDto@itemTagDto",
                                            "webLogParam@webLogParam",
                                            "waistCoverShowInfo@waistCoverShowInfo",
                                            "distributeType@distributeType"
                                        ],
                                        "renderer": {}
                                    }
                                ],
                                "renderer": {}
                            }
                        ],
                        "fieldKeys": [
                            "jumpUrl@jumpUrl",
                            "relItemId@relItemId"
                        ],
                        "renderer": {}
                    }
                }
            },
            "pageName": "CpsSearch",
            "pageVersion": 21
        }
    },
    "requestId": "753740159406037217"
}