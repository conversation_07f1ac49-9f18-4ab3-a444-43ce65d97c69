# 商品成交量查询功能说明

## 功能概述
商品成交量查询功能允许用户查询指定商品在指定时间范围内的每日成交量数据，并以表格形式展示结果。

## 界面布局

### 1. 控制区域（占界面高度10%）
- **时间范围选择器**：支持30天、60天、90天三个选项，默认选择30天
- **导入链接按钮**：自动读取剪切板中的商品链接并导入到表格
- **开始查询按钮**：启动数据采集流程
- **停止查询按钮**：中断正在进行的查询
- **导出数据按钮**：将查询结果导出为Excel文件
- **状态显示**：显示当前操作状态和进度

### 2. 数据展示区域（占界面高度90%）
- **表格布局**：
  - 第1列：商品标题（固定宽度300px）
  - 第2列：商品链接（固定宽度150px）
  - 第3列及以后：日期列（格式MM/DD，动态生成）
- **表格特性**：
  - 支持水平和垂直滚动
  - 每行高度固定为40px
  - 日期列从当天-1开始，向前推算到选择的天数

## 使用流程

### 1. 准备商品链接
- 复制要查询的商品链接到剪切板
- 支持多个链接，每行一个
- 链接格式：`https://app.kwaixiaodian.com/merchant/shop/detail?id=商品ID&hyId=kwaishop&layoutType=4`

### 2. 导入链接
- 点击"导入链接"按钮
- 系统自动读取剪切板内容并解析商品链接
- 链接将显示在表格的"商品链接"列中

### 3. 选择时间范围
- 在下拉选择器中选择查询的时间范围（30天/60天/90天）
- 更改时间范围会重新构建表格结构

### 4. 开始查询
- 点击"开始查询"按钮启动数据采集
- 系统按以下顺序处理：
  1. 查询第1个商品的标题
  2. 查询第1个商品每天的成交量数据
  3. 查询第2个商品的标题
  4. 查询第2个商品每天的成交量数据
  5. 以此类推...

### 5. 实时显示
- 商品标题获取后立即显示在表格中
- 每天的成交量数据获取后立即填入对应的日期列
- 状态栏显示当前处理进度

### 6. 导出数据
- 查询完成后，点击"导出数据"按钮
- 数据将导出为Excel文件，文件名格式：`商品成交量查询_YYYYMMDD_HHMMSS.xlsx`

## 数据处理规范

### 商品标题
- 最大显示50字符，超出部分用"..."省略
- 鼠标悬停显示完整标题

### 成交量数据
- 计算payOrderCnt数组中所有数值的总和
- 如果total为0，对应日期显示"0"
- 数据居中显示

## 技术特性

### 并发控制
- 同时最多处理1个商品的数据请求
- 请求间添加适当延迟避免过于频繁

### 错误处理
- 网络请求失败时显示错误信息
- 商品ID提取失败时跳过该商品
- 查询过程中可随时停止

### 数据持久化
- 查询结果可导出为Excel格式
- 支持重新查询和数据更新

## 注意事项

1. **Cookie要求**：需要先登录获取有效的Cookie才能进行查询
2. **网络连接**：需要稳定的网络连接访问快手小店API
3. **请求频率**：系统自动控制请求频率，避免被服务器限制
4. **数据准确性**：查询结果基于快手小店官方API，数据准确性由平台保证

## 故障排除

### 常见问题
1. **"未找到cookies"错误**：请先在"商品采集"标签页中点击"登录"按钮完成登录
2. **"无法提取商品ID"错误**：请检查商品链接格式是否正确
3. **查询无数据**：可能是商品在指定日期没有成交记录，属于正常情况
4. **导出失败**：请确保已安装pandas和openpyxl库：`pip install pandas openpyxl`

### 性能优化
- 建议单次查询的商品数量不超过50个
- 查询时间范围建议不超过90天
- 如需查询大量数据，建议分批进行
